
import { CalendarIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface EmptyAppointmentsProps {
  onSchedule: () => void;
}

const EmptyAppointments = ({ onSchedule }: EmptyAppointmentsProps) => {
  return (
    <div className="bg-white p-8 rounded-xl border border-slate-200 shadow-sm text-center">
      <div className="mb-4 text-slate-400">
        <CalendarIcon className="h-12 w-12 mx-auto" />
      </div>
      <h3 className="text-xl font-medium text-slate-900 mb-2">No appointments scheduled</h3>
      <p className="text-slate-600 mb-6">
        You don't have any upcoming appointments. Schedule your first appointment to get started.
      </p>
      <Button 
        onClick={onSchedule}
        className="bg-health-600 hover:bg-health-700 text-white"
      >
        Schedule Appointment
      </Button>
    </div>
  );
};

export default EmptyAppointments;
