
import { supabase as supabaseClient } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

// Import database initialization
import { initializeDatabase } from '@/utils/setupSupabase';

// Initialize database when the app loads
initializeDatabase()
  .then(status => {
    console.log("Database initialization complete with status:", status);
    if (!status.appointmentsTableExists) {
      console.warn("Appointments table does not exist - some features may not work properly");
    }
  })
  .catch(error => {
    console.error("Failed to initialize database:", error);
    console.log("Using local storage fallbacks for data");
  });

// Export the Supabase client for use in other files
export { supabaseClient as supabase };
