# Database Migration Plan: Supa<PERSON> to PostgreSQL

## Overview
This document outlines the complete migration strategy for converting the centpercent-care-connect healthcare application from Supabase Backend-as-a-Service to a standalone PostgreSQL database with Express.js backend.

## Current Architecture Analysis

### Frontend Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Library**: shadcn/ui + Tailwind CSS
- **State Management**: TanStack Query + React Context
- **Routing**: React Router v6

### Current Backend (Supabase)
- **Database**: PostgreSQL (managed by Supabase)
- **Authentication**: Supabase Auth with JWT tokens
- **API**: Auto-generated REST API
- **Real-time**: Supabase real-time subscriptions
- **Storage**: Browser localStorage for session persistence

### Database Schema
```sql
-- Users table
users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  phone_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)

-- Appointments table
appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  type TEXT NOT NULL,
  date DATE NOT NULL,
  time TEXT NOT NULL,
  doctor TEXT NOT NULL,
  location TEXT NOT NULL,
  status TEXT DEFAULT 'scheduled',
  canceled BOOLEAN DEFAULT false,
  notes TEXT,
  confirmation_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)

-- Results table
results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  date DATE NOT NULL,
  status TEXT NOT NULL,
  doctor TEXT NOT NULL,
  department TEXT,
  findings TEXT,
  recommendation TEXT,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)

-- Insurance info table (referenced but not fully implemented)
insurance_info (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  provider TEXT NOT NULL,
  policy_number TEXT NOT NULL,
  group_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

## Target Architecture

### New Backend Stack
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL (standalone)
- **ORM**: Prisma or raw SQL with pg library
- **Authentication**: JWT tokens with bcrypt password hashing
- **Validation**: Zod (already in use on frontend)
- **Environment**: dotenv for configuration

### Frontend Changes
- **HTTP Client**: Axios or fetch API
- **Authentication**: Custom JWT token management
- **Error Handling**: Enhanced error boundaries
- **Loading States**: Improved loading indicators

## Migration Strategy

### Phase 1: Backend Infrastructure Setup
1. **PostgreSQL Database Setup**
   - Choose hosting solution (local, Railway, Neon, AWS RDS)
   - Create database instance
   - Set up connection pooling
   - Configure SSL/security settings

2. **Express.js API Server**
   - Initialize Node.js project
   - Set up Express.js with TypeScript
   - Configure middleware (CORS, body parsing, logging)
   - Set up environment configuration
   - Create basic server structure

3. **Database Connection & Schema**
   - Install PostgreSQL client (pg or Prisma)
   - Create database connection module
   - Implement schema migration scripts
   - Set up database seeding (if needed)

### Phase 2: Authentication System
1. **User Management**
   - Create User model/schema
   - Implement password hashing with bcrypt
   - Create user registration endpoint
   - Create user login endpoint
   - Implement JWT token generation/validation

2. **Authentication Middleware**
   - Create JWT verification middleware
   - Implement protected route handling
   - Add token refresh mechanism
   - Create logout functionality

### Phase 3: API Endpoints
1. **User Endpoints**
   - GET /api/users/profile (get current user)
   - PUT /api/users/profile (update user profile)
   - POST /api/auth/register
   - POST /api/auth/login
   - POST /api/auth/logout
   - POST /api/auth/refresh

2. **Appointments Endpoints**
   - GET /api/appointments (list user appointments)
   - POST /api/appointments (create appointment)
   - PUT /api/appointments/:id (update appointment)
   - DELETE /api/appointments/:id (cancel appointment)

3. **Results Endpoints**
   - GET /api/results (list user results)
   - POST /api/results (create result - admin only)
   - GET /api/results/:id (get specific result)

4. **Insurance Endpoints**
   - GET /api/insurance (get user insurance info)
   - POST /api/insurance (add insurance info)
   - PUT /api/insurance (update insurance info)

### Phase 4: Frontend Migration
1. **API Client Setup**
   - Create API client utility
   - Implement request/response interceptors
   - Add error handling
   - Set up base URL configuration

2. **Authentication Context Update**
   - Replace Supabase auth with custom JWT auth
   - Update login/register functions
   - Implement token storage and refresh
   - Update auth state management

3. **Data Fetching Updates**
   - Replace Supabase queries with API calls
   - Update TanStack Query configurations
   - Implement proper error handling
   - Add loading states

### Phase 5: Testing & Cleanup
1. **Testing**
   - Test all authentication flows
   - Verify all CRUD operations
   - Test error scenarios
   - Performance testing

2. **Cleanup**
   - Remove Supabase dependencies
   - Clean up unused code
   - Update environment variables
   - Update documentation

## Technical Considerations

### Security
- **Password Security**: bcrypt with salt rounds
- **JWT Security**: Secure secret keys, appropriate expiration
- **API Security**: Rate limiting, input validation
- **Database Security**: Parameterized queries, connection security

### Performance
- **Database**: Connection pooling, query optimization
- **API**: Response caching, pagination
- **Frontend**: Optimistic updates, proper loading states

### Error Handling
- **Backend**: Structured error responses, logging
- **Frontend**: User-friendly error messages, retry mechanisms
- **Database**: Transaction handling, constraint violations

### Environment Configuration
```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/centpercent_care
DB_HOST=localhost
DB_PORT=5432
DB_NAME=centpercent_care
DB_USER=username
DB_PASSWORD=password

# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Server
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173
```

## Deployment Strategy

### Development Environment
1. Local PostgreSQL instance
2. Local Express.js server (port 3001)
3. Local React dev server (port 5173)

### Production Environment
1. Hosted PostgreSQL (Railway/Neon/AWS RDS)
2. Deployed Express.js API (Railway/Vercel/Heroku)
3. Deployed React app (Vercel/Netlify)

## Risk Mitigation

### Data Loss Prevention
- No existing data to migrate (confirmed by user)
- Database backups before any changes
- Rollback procedures documented

### Downtime Minimization
- Parallel development approach
- Feature flags for gradual rollout
- Comprehensive testing before deployment

### Compatibility Issues
- Maintain same API contracts where possible
- Gradual migration of components
- Fallback mechanisms during transition

## Success Criteria

### Functional Requirements
- [ ] All existing features work identically
- [ ] User authentication flows work seamlessly
- [ ] All CRUD operations function correctly
- [ ] Data persistence works reliably

### Non-Functional Requirements
- [ ] Response times under 500ms for API calls
- [ ] 99.9% uptime for production deployment
- [ ] Secure authentication implementation
- [ ] Proper error handling and user feedback

### Technical Requirements
- [ ] Clean, maintainable code structure
- [ ] Comprehensive error handling
- [ ] Proper TypeScript typing
- [ ] Documentation updated

## Timeline Estimate

- **Phase 1**: 2-3 days (Backend setup)
- **Phase 2**: 2-3 days (Authentication)
- **Phase 3**: 3-4 days (API endpoints)
- **Phase 4**: 3-4 days (Frontend migration)
- **Phase 5**: 1-2 days (Testing & cleanup)

**Total Estimated Time**: 11-16 days

## Next Steps

1. Confirm PostgreSQL hosting choice
2. Set up development environment
3. Begin Phase 1 implementation
4. Regular progress reviews and testing
5. Documentation updates throughout process
