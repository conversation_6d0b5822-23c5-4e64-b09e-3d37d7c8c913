
import { format } from "date-fns";
import { Button } from "@/components/ui/button";

interface AppointmentBookingCompleteProps {
  confirmationNumber: string;
  appointmentTypeName: string;
  appointmentTypeIcon: JSX.Element | null;
  locationName: string;
  locationAddress: string;
  date: string;
  time: string;
  email: string;
  onViewAppointments: () => void;
}

const AppointmentBookingComplete = ({
  confirmationNumber,
  appointmentTypeName,
  appointmentTypeIcon,
  locationName,
  locationAddress,
  date,
  time,
  email,
  onViewAppointments
}: AppointmentBookingCompleteProps) => {
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden animate-fade-in">
      <div className="p-8 text-center">
        <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-slate-900 mb-2">
          Appointment Confirmed!
        </h2>
        <p className="text-slate-600 mb-6">
          Your appointment has been successfully scheduled. A confirmation email has been sent to {email}.
        </p>
        <div className="bg-slate-50 rounded-lg p-6 border border-slate-200 mb-6 mx-auto max-w-md">
          <div className="text-center mb-4">
            <span className="text-sm font-medium text-slate-500">Confirmation Number</span>
            <div className="text-xl font-mono font-bold text-slate-900 mt-1">{confirmationNumber}</div>
          </div>
          <div className="flex items-start mb-4 justify-center">
            <div
              className="w-10 h-10 rounded-full bg-health-100 text-health-600 flex items-center justify-center mr-4 flex-shrink-0"
            >
              {appointmentTypeIcon}
            </div>
            <div className="text-left">
              <h3 className="font-medium text-slate-900">
                {appointmentTypeName} Appointment
              </h3>
              <p className="text-sm text-slate-600">
                {locationName} - {locationAddress}
              </p>
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center gap-4">
            <div className="flex items-center">
              <svg className="w-4 h-4 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="text-slate-600">{date ? formatDate(date) : ""}</span>
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-slate-600">{time}</span>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            asChild
            variant="outline"
            className="border-health-200 text-health-700"
          >
            <a href={`data:text/calendar;charset=utf-8,${encodeURIComponent(`BEGIN:VCALENDAR\nVERSION:2.0\nBEGIN:VEVENT\nDTSTART:${date.replace(/-/g, '')}T${time.replace(/:/g, '').replace(/ AM/g, '').replace(/ PM/g, '')}\nDTEND:${date.replace(/-/g, '')}T${time.replace(/:/g, '').replace(/ AM/g, '').replace(/ PM/g, '')}\nSUMMARY:${appointmentTypeName} Appointment\nLOCATION:${locationName}\nEND:VEVENT\nEND:VCALENDAR`)}`} download="appointment.ics">
              Add to Calendar
            </a>
          </Button>
          <Button
            onClick={onViewAppointments}
            className="bg-health-600 hover:bg-health-700 text-white"
          >
            View My Appointments
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AppointmentBookingComplete;
