import { Pool } from 'pg';
import * as dotenv from 'dotenv';
import { UserModel, CreateUserData } from './models/User';

// Load environment variables
dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'centpercent_care',
  user: process.env.DB_USER || 'centpercent_app',
  password: process.env.DB_PASSWORD,
});

async function testUserModel() {
  const userModel = new UserModel(pool);
  
  try {
    console.log('🧪 Testing User Model...');
    
    // Test 1: Check if email exists (should be false for new email)
    const testEmail = '<EMAIL>';
    const emailExists = await userModel.emailExists(testEmail);
    console.log(`📧 Email ${testEmail} exists:`, emailExists);
    
    // Test 2: Try to find non-existent user
    const nonExistentUser = await userModel.findByEmail(testEmail);
    console.log('👤 Non-existent user found:', nonExistentUser);
    
    // Test 3: Get user count
    const userCount = await userModel.getUserCount();
    console.log('📊 Current user count:', userCount);
    
    // Test 4: Test interfaces (TypeScript compilation check)
    const sampleUserData: CreateUserData = {
      email: '<EMAIL>',
      password: 'testpassword',
      firstName: 'John',
      lastName: 'Doe',
      phoneNumber: '+1234567890'
    };
    console.log('✅ CreateUserData interface works:', !!sampleUserData);
    
    console.log('🎉 User Model tests completed successfully!');
    
  } catch (error) {
    console.error('❌ User Model test failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the test
if (require.main === module) {
  testUserModel()
    .then(() => {
      console.log('\n🏁 User Model test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 User Model test failed:', error);
      process.exit(1);
    });
}

export { testUserModel };
