
import { format } from "date-fns";
import { Loader2 } from "lucide-react";
import { 
  AlertDialog,
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog";
import { Appointment } from "./AppointmentsList";

interface AppointmentCancelDialogProps {
  isOpen: boolean;
  isCancelling: boolean;
  appointment: Appointment | null;
  onOpenChange: (open: boolean) => void;
  onConfirmCancel: () => void;
}

const AppointmentCancelDialog = ({
  isOpen,
  isCancelling,
  appointment,
  onOpenChange,
  onConfirmCancel
}: AppointmentCancelDialogProps) => {
  if (!appointment) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to cancel your {appointment.type} appointment on{" "}
            {format(new Date(appointment.date), "MMMM d, yyyy")} at {appointment.time}?
            <p className="mt-2 font-medium">This action cannot be undone.</p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isCancelling}>Keep Appointment</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirmCancel}
            disabled={isCancelling}
            className="bg-red-600 hover:bg-red-700"
          >
            {isCancelling ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cancelling...
              </>
            ) : (
              "Yes, Cancel Appointment"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default AppointmentCancelDialog;
