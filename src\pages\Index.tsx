import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import HeroSection from "@/components/HeroSection";
import ServiceCard from "@/components/ServiceCard";
import TestimonialCard from "@/components/TestimonialCard";

const Index = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const services = [
    {
      title: "Ultrasound",
      description: "Non-invasive imaging using sound waves to visualize internal organs and structures.",
      icon: "ultrasound" as const,
      features: [
        "Abdominal ultrasound",
        "Obstetric ultrasound",
        "Vascular ultrasound",
        "Thyroid ultrasound",
        "Quick and painless procedure"
      ],
      price: "$150",
      learnMoreLink: "/services#ultrasound",
      appointmentLink: "/appointments",
      highlighted: false,
    },
    {
      title: "ECG",
      description: "Electrocardiogram testing to evaluate heart rhythm and detect cardiac conditions.",
      icon: "ecg" as const,
      features: [
        "Heart rhythm analysis",
        "Cardiac condition detection",
        "Arrhythmia screening",
        "Heart rate monitoring",
        "Quick and non-invasive"
      ],
      price: "$120",
      learnMoreLink: "/services#ecg",
      appointmentLink: "/appointments",
      highlighted: true,
    },
    {
      title: "X-Ray",
      description: "Quick and effective diagnostic imaging for bones, chest, and other structures.",
      icon: "xray" as const,
      features: [
        "Chest X-ray",
        "Bone X-ray",
        "Dental X-ray",
        "Joint X-ray",
        "Fast results available"
      ],
      price: "$95",
      learnMoreLink: "/services#xray",
      appointmentLink: "/appointments",
      highlighted: false,
    },
  ];

  const testimonials = [
    {
      content: "The staff was incredibly professional and made me feel at ease during my MRI. The facility is modern and clean, and I received my results quickly.",
      author: "Sarah Johnson",
      role: "Patient",
      rating: 5,
    },
    {
      content: "As a referring physician, I've been consistently impressed with the quality of imaging and reporting from Cent'Percent HealthCare. Their radiologists provide thorough and accurate interpretations.",
      author: "Dr. Michael Chen",
      role: "Cardiologist",
      rating: 5,
    },
    {
      content: "I was nervous about my first ultrasound, but the technician was so kind and explained everything. The online portal made it easy to schedule and access my results.",
      author: "Emily Rodriguez",
      role: "Patient",
      rating: 4,
    },
  ];

  const stats = [
    { value: "99%", label: "Patient Satisfaction" },
    { value: "25+", label: "Expert Radiologists" },
    { value: "15k+", label: "Patients Served" },
    { value: "3", label: "Convenient Locations" },
  ];

  return (
    <>
      <Navbar />
      
      <main>
        <HeroSection />

        {/* Services Section */}
        <section className="py-20 bg-white" id="services">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                Our Services
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Advanced Imaging Services
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                State-of-the-art diagnostic imaging services with a focus on patient comfort and accurate results.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <ServiceCard
                  key={index}
                  {...service}
                />
              ))}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="py-20 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-20'}`}>
                <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                  About Us
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                  Your Health Is Our Priority
                </h2>
                <p className="text-lg text-slate-600 mb-6">
                  At Cent'Percent HealthCare, we combine cutting-edge imaging technology with compassionate care. Our team of board-certified radiologists and technicians are dedicated to providing the highest quality diagnostic services.
                </p>
                <p className="text-lg text-slate-600 mb-8">
                  From our state-of-the-art equipment to our patient-centered approach, we ensure that every aspect of your care exceeds expectations. We're committed to making your diagnostic imaging experience as comfortable and efficient as possible.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    asChild
                    className="bg-health-600 hover:bg-health-700 text-white neo-button"
                  >
                    <Link to="/about">Learn More About Us</Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    className="border-health-200 text-health-700"
                  >
                    <Link to="/team">Meet Our Team</Link>
                  </Button>
                </div>
              </div>
              
              <div className={`relative transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-20'}`}>
                <div className="relative">
                  <div className="absolute -inset-4 bg-health-200 rounded-2xl transform rotate-3 opacity-30"></div>
                  <img
                    src="https://images.unsplash.com/photo-1631815588090-d1bcbe9a8545?auto=format&fit=crop&q=80&w=800&h=500"
                    alt="Modern healthcare facility"
                    className="rounded-xl shadow-lg relative z-10"
                    loading="lazy"
                  />
                  <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg animate-fade-in" style={{ animationDelay: "0.6s" }}>
                    <div className="flex items-center gap-3">
                      <div className="bg-health-100 p-2 rounded-full">
                        <svg className="w-5 h-5 text-health-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-slate-900 font-medium">Accredited Facility</p>
                        <p className="text-xs text-slate-600">ACR & JCAHO certified</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="rounded-lg overflow-hidden shadow-md">
                    <img
                      src="https://images.unsplash.com/photo-1581595219315-a187dd40c322?auto=format&fit=crop&q=80&w=400&h=300"
                      alt="Advanced medical equipment"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                  <div className="rounded-lg overflow-hidden shadow-md">
                    <img
                      src="https://images.unsplash.com/photo-1666214280548-76f5ee8ea68d?auto=format&fit=crop&q=80&w=400&h=300"
                      alt="Caring medical staff"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-gradient-to-r from-health-600 to-health-700 text-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center animate-fade-in" style={{ animationDelay: `${0.1 * index}s` }}>
                  <div className="text-4xl md:text-5xl font-bold mb-2">{stat.value}</div>
                  <div className="text-sm md:text-base text-white/80">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                Testimonials
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                What Our Patients Say
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Hear from patients and healthcare providers who trust Cent'Percent HealthCare for their diagnostic imaging needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  {...testimonial}
                />
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-health-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2">
                <div className="p-8 md:p-12 flex flex-col justify-center">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Ready to Schedule Your Appointment?
                  </h2>
                  <p className="text-slate-600 mb-8">
                    Register now to access our online scheduling system, view your results, and manage your health information securely.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      asChild
                      className="bg-health-600 hover:bg-health-700 text-white neo-button"
                    >
                      <Link to="/register">Create an Account</Link>
                    </Button>
                    <Button
                      asChild
                      variant="outline"
                      className="border-health-200 text-health-700"
                    >
                      <Link to="/login">Already Registered? Sign In</Link>
                    </Button>
                  </div>
                </div>
                <div className="relative h-64 md:h-auto">
                  <img
                    src="https://images.unsplash.com/photo-*************-fbdc51b2763b?auto=format&fit=crop&q=80&w=600"
                    alt="Patient consultation"
                    className="absolute inset-0 w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-l from-transparent to-health-900/30"></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default Index;
