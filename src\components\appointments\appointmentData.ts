import { 
  Ultrasound, 
  XRay, 
  ECG, 
  BloodTestIcon, 
  UrineTestIcon, 
  PathologyIcon 
} from "@/components/CustomIcons";
import { ReactElement } from "react";

// Define a type for the appointment types
export interface AppointmentType {
  id: string;
  name: string;
  icon: ReactElement;
  category: string;
}

export const appointmentTypes: AppointmentType[] = [
  // Imaging Services
  { id: "ultrasound", name: "Ultrasound", icon: Ultrasound({ size: 18 }), category: "Imaging" },
  { id: "xray", name: "X-Ray", icon: XRay({ size: 18 }), category: "Imaging" },
  { id: "ecg", name: "ECG", icon: ECG({ size: 18 }), category: "Imaging" },
  
  // Blood Tests
  { id: "metabolic-panel", name: "Comprehensive Metabolic Panel", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "lipid-profile", name: "Lipid Profile", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "cbc", name: "Complete Blood Count (CBC)", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "glucose", name: "Glucose Testing", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "thyroid", name: "Thyroid Function Panel", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "blood-chemistry", name: "Comprehensive Blood Chemistry", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "iron-studies", name: "Iron Studies", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  { id: "vitamin-d", name: "Vitamin D Test", icon: BloodTestIcon({ size: 18 }), category: "Blood Test" },
  
  // Urine Tests
  { id: "urinalysis", name: "Urinalysis", icon: UrineTestIcon({ size: 18 }), category: "Urine Test" },
  { id: "24h-urine", name: "24-Hour Urine Collection", icon: UrineTestIcon({ size: 18 }), category: "Urine Test" },
  { id: "drug-screening", name: "Drug Screening", icon: UrineTestIcon({ size: 18 }), category: "Urine Test" },
  
  // Specialty Tests
  { id: "thyroid-panel", name: "Thyroid Function Panel", icon: PathologyIcon({ size: 18 }), category: "Specialty Test" },
  { id: "vitamin-panel", name: "Vitamin Panel", icon: PathologyIcon({ size: 18 }), category: "Specialty Test" },
  { id: "hormone-panel", name: "Hormone Panel", icon: PathologyIcon({ size: 18 }), category: "Specialty Test" },
];

export const locations = [
  { id: "main", name: "Main Clinic", address: "123 Medical Drive, Suite 101" },
  { id: "north", name: "North Branch", address: "456 Health Avenue, Building B" },
  { id: "east", name: "East Center", address: "789 Wellness Road" },
];

export const timeSlots = [
  "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM",
  "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM"
];

export const categories = ["All", "Imaging", "Blood Test", "Urine Test", "Specialty Test"];
