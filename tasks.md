# Migration Tasks: Supabase to PostgreSQL

## Phase 1: Backend Infrastructure Setup

### PostgreSQL Database Setup

- [x] Choose PostgreSQL hosting solution (local/Railway/Neon/AWS RDS) ✅ **Local PostgreSQL**
- [x] Create PostgreSQL database instance ✅ **PostgreSQL 16.2 installed**
- [x] Configure database connection settings ✅ **Connection working**
- [x] Set up SSL/security configurations ✅ **Default local setup**
- [x] Test database connectivity ✅ **Connection test passed**
- [x] Set up connection pooling ✅ **pg Pool configured**
- [x] Create database user with appropriate permissions ✅ **centpercent_app user created**

### Express.js API Server Setup

- [x] Create new backend directory (`/backend` or `/server`) ✅ **`/backend` directory created**
- [x] Initialize Node.js project (`npm init`) ✅ **package.json created**
- [x] Install Express.js and TypeScript dependencies ✅ **460 packages installed**
- [x] Set up TypeScript configuration (`tsconfig.json`) ✅ **TypeScript configured**
- [x] Create basic Express server structure ✅ **server.ts created**
- [x] Configure CORS middleware ✅ **CORS configured for localhost:5173**
- [x] Set up body parsing middleware ✅ **JSON/URL-encoded parsing**
- [x] Add request logging middleware ✅ **Request logging implemented**
- [x] Create environment configuration with dotenv ✅ **.env file created**
- [x] Set up development scripts (dev, build, start) ✅ **npm scripts configured**

### Database Connection & Schema

- [x] Install PostgreSQL client library (`pg` and `@types/pg`) ✅ **pg@8.11.3 installed**
- [x] Create database connection module ✅ **Pool connection in server.ts**
- [x] Create database schema migration files ✅ **001_create_tables.sql created**
- [x] Implement UUID extension setup ✅ **uuid-ossp extension enabled**
- [x] Create users table migration ✅ **Users table with auth fields**
- [x] Create appointments table migration ✅ **Appointments table created**
- [x] Create results table migration ✅ **Results table created**
- [x] Create insurance_info table migration ✅ **Insurance table created**
- [x] Set up database seeding scripts (optional) ✅ **Migration tracking system**
- [x] Test all migrations ✅ **All 5 tables created successfully**

## Phase 2: Authentication System

### User Management Backend

- [ ] Install bcrypt and jsonwebtoken dependencies
- [ ] Create User model/interface
- [ ] Implement password hashing utility
- [ ] Create JWT token generation utility
- [ ] Create JWT token verification utility
- [ ] Implement user registration logic
- [ ] Implement user login logic
- [ ] Create password validation rules
- [ ] Add email validation

### Authentication Middleware

- [ ] Create JWT verification middleware
- [ ] Implement protected route decorator
- [ ] Add token refresh mechanism
- [ ] Create logout functionality
- [ ] Add rate limiting for auth endpoints
- [ ] Implement proper error handling for auth failures

## Phase 3: API Endpoints

### User Endpoints

- [ ] POST /api/auth/register - User registration
- [ ] POST /api/auth/login - User login
- [ ] POST /api/auth/logout - User logout
- [ ] POST /api/auth/refresh - Token refresh
- [ ] GET /api/users/profile - Get current user profile
- [ ] PUT /api/users/profile - Update user profile
- [ ] Add input validation for all user endpoints
- [ ] Add proper error responses

### Appointments Endpoints

- [ ] GET /api/appointments - List user appointments
- [ ] POST /api/appointments - Create new appointment
- [ ] GET /api/appointments/:id - Get specific appointment
- [ ] PUT /api/appointments/:id - Update appointment
- [ ] DELETE /api/appointments/:id - Cancel appointment
- [ ] Add appointment validation rules
- [ ] Implement appointment status management
- [ ] Add pagination for appointments list

### Results Endpoints

- [ ] GET /api/results - List user results
- [ ] POST /api/results - Create result (admin only)
- [ ] GET /api/results/:id - Get specific result
- [ ] PUT /api/results/:id - Update result (admin only)
- [ ] Add results validation rules
- [ ] Implement proper authorization for admin endpoints
- [ ] Add pagination for results list

### Insurance Endpoints

- [ ] GET /api/insurance - Get user insurance info
- [ ] POST /api/insurance - Add insurance info
- [ ] PUT /api/insurance - Update insurance info
- [ ] DELETE /api/insurance - Remove insurance info
- [ ] Add insurance validation rules

## Phase 4: Frontend Migration

### API Client Setup

- [ ] Create API client utility (`src/lib/api.ts`)
- [ ] Set up Axios or fetch configuration
- [ ] Implement request interceptors
- [ ] Implement response interceptors
- [ ] Add error handling utilities
- [ ] Configure base URL and timeout settings
- [ ] Create API endpoint constants

### Authentication Context Update

- [ ] Update AuthContext to use new API
- [ ] Replace Supabase auth with JWT auth
- [ ] Implement token storage in localStorage
- [ ] Update login function
- [ ] Update register function
- [ ] Update logout function
- [ ] Add token refresh logic
- [ ] Update auth state management
- [ ] Remove Supabase auth dependencies

### Data Fetching Updates

- [ ] Update Dashboard.tsx to use new API
- [ ] Update Appointments.tsx to use new API
- [ ] Update Results.tsx to use new API
- [ ] Update user profile components
- [ ] Replace all Supabase queries with API calls
- [ ] Update TanStack Query configurations
- [ ] Implement proper error handling
- [ ] Add loading states for all operations
- [ ] Update type definitions

### Component Updates

- [ ] Update Login.tsx component
- [ ] Update Register.tsx component
- [ ] Update AuthForm.tsx component
- [ ] Remove Supabase-specific error handling
- [ ] Update error messages for new API responses
- [ ] Test all form submissions

## Phase 5: Testing & Cleanup

### Backend Testing

- [ ] Test user registration endpoint
- [ ] Test user login endpoint
- [ ] Test JWT token validation
- [ ] Test protected routes
- [ ] Test appointments CRUD operations
- [ ] Test results CRUD operations
- [ ] Test insurance CRUD operations
- [ ] Test error scenarios
- [ ] Test input validation
- [ ] Performance test API endpoints

### Frontend Testing

- [ ] Test user registration flow
- [ ] Test user login flow
- [ ] Test user logout flow
- [ ] Test dashboard data loading
- [ ] Test appointments management
- [ ] Test results viewing
- [ ] Test error handling
- [ ] Test loading states
- [ ] Cross-browser testing

### Integration Testing

- [ ] Test complete user journey
- [ ] Test authentication persistence
- [ ] Test API error handling
- [ ] Test network failure scenarios
- [ ] Test concurrent user sessions

### Cleanup Tasks

- [ ] Remove @supabase/supabase-js dependency
- [ ] Remove Supabase client files
- [ ] Remove Supabase types
- [ ] Remove Supabase utilities
- [ ] Clean up unused imports
- [ ] Update package.json dependencies
- [ ] Remove supabase directory
- [ ] Update environment variables
- [ ] Update .gitignore if needed

### Documentation Updates

- [ ] Update README.md with new setup instructions
- [ ] Document API endpoints
- [ ] Update environment variables documentation
- [ ] Create database setup guide
- [ ] Update deployment instructions
- [ ] Document authentication flow
- [ ] Add troubleshooting guide

## Environment Setup Tasks

### Development Environment

- [ ] Set up local PostgreSQL database
- [ ] Configure backend environment variables
- [ ] Configure frontend environment variables
- [ ] Update development scripts
- [ ] Test local development setup

### Production Preparation

- [ ] Choose production PostgreSQL hosting
- [ ] Choose backend hosting platform
- [ ] Set up production environment variables
- [ ] Configure production database
- [ ] Set up CI/CD pipeline (optional)
- [ ] Plan deployment strategy

## Final Verification

### Functional Verification

- [ ] All existing features work identically
- [ ] User authentication flows work seamlessly
- [ ] All CRUD operations function correctly
- [ ] Data persistence works reliably
- [ ] Error handling works properly

### Performance Verification

- [ ] API response times under 500ms
- [ ] Frontend loading times acceptable
- [ ] Database queries optimized
- [ ] No memory leaks in backend
- [ ] Proper connection pooling working

### Security Verification

- [ ] Passwords properly hashed
- [ ] JWT tokens secure
- [ ] API endpoints properly protected
- [ ] Input validation working
- [ ] SQL injection prevention verified
- [ ] CORS properly configured

## Progress Tracking

**Overall Progress: 0/100+ tasks completed**

### Phase Completion Status

- [ ] Phase 1: Backend Infrastructure Setup (0/20 tasks)
- [ ] Phase 2: Authentication System (0/15 tasks)
- [ ] Phase 3: API Endpoints (0/25 tasks)
- [ ] Phase 4: Frontend Migration (0/25 tasks)
- [ ] Phase 5: Testing & Cleanup (0/30 tasks)

### Notes

- Update this file as tasks are completed
- Add any additional tasks discovered during implementation
- Note any blockers or issues in the comments
- Estimate completion dates for each phase
