import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { FileText, Download, Search, Calendar } from "lucide-react";
import { Ultrasound, XRay, MRI } from "@/components/CustomIcons";
import { supabase } from "@/lib/supabase";

// Mock data for test results
const mockResults = [
  {
    id: "res-1",
    type: "Ultrasound",
    title: "Abdominal Ultrasound",
    date: "2023-10-10",
    status: "Ready",
    doctor: "<PERSON><PERSON> <PERSON>",
    department: "Radiology",
    findings: "No abnormalities detected. All organs appear normal in size and texture. No free fluid in the peritoneal cavity.",
    recommendation: "Follow-up in 1 year for routine screening.",
    icon: <Ultrasound size={18} />,
  },
  {
    id: "res-2",
    type: "X-Ray",
    title: "Chest X-Ray",
    date: "2023-09-15",
    status: "Ready",
    doctor: "Dr. Smith",
    department: "Radiology",
    findings: "Clear lung fields. Heart size within normal limits. No evidence of pneumonia or effusion.",
    recommendation: "No follow-up necessary at this time.",
    icon: <XRay size={18} />,
  },
  {
    id: "res-3",
    type: "MRI",
    title: "Knee MRI",
    date: "2023-08-20",
    status: "Ready",
    doctor: "Dr. Williams",
    department: "Orthopedics",
    findings: "Mild degenerative changes. No meniscal tears. ACL and PCL intact. Mild joint effusion noted.",
    recommendation: "Physical therapy recommended. Follow-up with orthopedic specialist in 3 months.",
    icon: <MRI size={18} />,
  },
];

// Mock upcoming test appointments
const upcomingTests = [
  {
    id: "test-1",
    type: "MRI",
    title: "Brain MRI",
    date: "2023-11-12",
    time: "10:30 AM",
    location: "Main Clinic, Room 305",
    doctor: "Dr. Brown",
    icon: <MRI size={18} />,
    preparation: "No food or drink 4 hours before the scan. Remove all metal objects prior to the scan.",
  },
];

const Results = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResults, setFilteredResults] = useState(mockResults);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedResult, setSelectedResult] = useState<typeof mockResults[0] | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [supabaseResults, setSupabaseResults] = useState<any[]>([]);
  const [useLocalData, setUseLocalData] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    } else if (user) {
      fetchResults();
    }
  }, [isAuthenticated, navigate, user]);

  const fetchResults = async () => {
    if (!user) return;
    
    setIsLoadingData(true);
    try {
      const { data, error } = await supabase
        .from('results')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false });
      
      if (error) {
        console.error('Error fetching results from Supabase:', error);
        
        // If table doesn't exist, use mock data
        if (error.code === '42P01') {
          setUseLocalData(true);
        } else {
          toast({
            title: "Data fetch error",
            description: "Failed to load your test results",
            variant: "destructive",
          });
        }
      } else if (data && data.length > 0) {
        setSupabaseResults(data);
        setUseLocalData(false);
      }
    } catch (error) {
      console.error('Error in results fetch:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    // Filter results based on search term and active tab
    const resultsToFilter = useLocalData ? mockResults : supabaseResults;
    
    const filtered = resultsToFilter.filter((result) => {
      const titleField = useLocalData ? result.title : (result.title || result.type);
      const typeField = result.type;
      const doctorField = result.doctor;
      
      const matchesSearch = 
        titleField.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doctorField.toLowerCase().includes(searchTerm.toLowerCase()) ||
        typeField.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (activeTab === "all") return matchesSearch;
      return matchesSearch && typeField.toLowerCase() === activeTab.toLowerCase();
    });
    
    setFilteredResults(filtered);
  }, [searchTerm, activeTab, useLocalData, supabaseResults]);

  const handleDownload = (resultId: string) => {
    // In a real app, this would initiate a file download
    toast({
      title: "Download started",
      description: "Your test result is being downloaded as a PDF.",
    });
  };

  const handleShare = (resultId: string) => {
    // In a real app, this would open a sharing modal or options
    toast({
      title: "Share option coming soon",
      description: "This feature will be available in the next update.",
    });
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <>
      <Navbar />
      <div className="pt-20 pb-16 min-h-screen bg-gradient-to-b from-health-50/50 to-white">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Test Results
          </h1>
          <p className="text-slate-600 mb-8">
            View and manage your medical imaging results securely
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden mb-8">
                <div className="p-6 border-b border-slate-200">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <h2 className="text-xl font-semibold text-slate-900">
                      Your Results
                    </h2>
                    <div className="relative w-full sm:w-64">
                      <Input
                        placeholder="Search results..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pr-10"
                      />
                      <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-slate-50 border-b border-slate-200">
                  <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-4 bg-slate-100">
                      <TabsTrigger value="all">All</TabsTrigger>
                      <TabsTrigger value="ultrasound">Ultrasound</TabsTrigger>
                      <TabsTrigger value="xray">X-Ray</TabsTrigger>
                      <TabsTrigger value="mri">MRI</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                {filteredResults.length > 0 ? (
                  <div className="divide-y divide-slate-100">
                    {filteredResults.map((result) => (
                      <div
                        key={result.id}
                        className={`p-4 hover:bg-slate-50 transition-colors cursor-pointer ${selectedResult?.id === result.id ? 'bg-slate-50' : ''}`}
                        onClick={() => setSelectedResult(result)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-full ${
                            result.type === "Ultrasound" ? "bg-blue-100 text-blue-600" : 
                            result.type === "X-Ray" ? "bg-purple-100 text-purple-600" : 
                            "bg-green-100 text-green-600"
                          }`}>
                            {result.icon}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-medium text-slate-900">
                                  {result.title}
                                </h3>
                                <p className="text-sm text-slate-600">
                                  {formatDate(result.date)} • {result.doctor}
                                </p>
                              </div>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                result.status === "Ready" ? "bg-green-100 text-green-800" : 
                                result.status === "Pending" ? "bg-yellow-100 text-yellow-800" : 
                                "bg-blue-100 text-blue-800"
                              }`}>
                                {result.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 text-center">
                    <FileText className="mx-auto text-slate-300 mb-2" size={40} />
                    <p className="text-slate-600">No results found</p>
                    <p className="text-sm text-slate-500 mt-1">Try adjusting your search terms</p>
                  </div>
                )}
              </div>
            </div>

            <div className="lg:col-span-1">
              {selectedResult ? (
                <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden sticky top-24 animate-fade-in">
                  <div className="p-6 border-b border-slate-200 flex justify-between items-center">
                    <h3 className="font-semibold text-slate-900">Result Details</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs border-slate-200"
                      onClick={() => setSelectedResult(null)}
                    >
                      Close
                    </Button>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className={`p-3 rounded-full ${
                        selectedResult.type === "Ultrasound" ? "bg-blue-100 text-blue-600" : 
                        selectedResult.type === "X-Ray" ? "bg-purple-100 text-purple-600" : 
                        "bg-green-100 text-green-600"
                      }`}>
                        {selectedResult.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-slate-900">
                          {selectedResult.title}
                        </h3>
                        <p className="text-sm text-slate-500">
                          {selectedResult.type} • {selectedResult.department}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div>
                        <p className="text-xs text-slate-500 mb-1">Date</p>
                        <p className="text-sm font-medium text-slate-900 flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(selectedResult.date)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-500 mb-1">Doctor</p>
                        <p className="text-sm font-medium text-slate-900">
                          {selectedResult.doctor}
                        </p>
                      </div>
                    </div>

                    <div className="mb-6">
                      <p className="text-xs text-slate-500 mb-1">Findings</p>
                      <p className="text-sm text-slate-700 bg-slate-50 p-3 rounded border border-slate-100">
                        {selectedResult.findings}
                      </p>
                    </div>

                    <div className="mb-6">
                      <p className="text-xs text-slate-500 mb-1">Recommendation</p>
                      <p className="text-sm text-slate-700 bg-slate-50 p-3 rounded border border-slate-100">
                        {selectedResult.recommendation}
                      </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        onClick={() => handleDownload(selectedResult.id)}
                        className="bg-health-600 hover:bg-health-700 text-white flex-1"
                      >
                        <Download size={16} className="mr-2" />
                        Download PDF
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleShare(selectedResult.id)}
                        className="border-health-200 text-health-700 flex-1"
                      >
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden mb-6">
                    <div className="p-6 border-b border-slate-200">
                      <h3 className="font-semibold text-slate-900">Upcoming Tests</h3>
                    </div>
                    {upcomingTests.length > 0 ? (
                      <div className="divide-y divide-slate-100">
                        {upcomingTests.map((test) => (
                          <div key={test.id} className="p-4">
                            <div className="flex items-start gap-3">
                              <div className={`p-2 rounded-full ${
                                test.type === "Ultrasound" ? "bg-blue-100 text-blue-600" : 
                                test.type === "X-Ray" ? "bg-purple-100 text-purple-600" : 
                                "bg-green-100 text-green-600"
                              }`}>
                                {test.icon}
                              </div>
                              <div>
                                <h4 className="font-medium text-slate-900">
                                  {test.title}
                                </h4>
                                <p className="text-sm text-slate-600">
                                  {formatDate(test.date)} at {test.time}
                                </p>
                                <p className="text-xs text-slate-500 mt-1">
                                  {test.location}
                                </p>
                                <div className="mt-2 pt-2 border-t border-slate-100">
                                  <p className="text-xs font-medium text-slate-700">Preparation:</p>
                                  <p className="text-xs text-slate-600 mt-1">{test.preparation}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-6 text-center">
                        <Calendar className="mx-auto text-slate-300 mb-2" size={30} />
                        <p className="text-slate-600">No upcoming tests</p>
                        <Button 
                          className="mt-4 text-sm bg-health-600 hover:bg-health-700 text-white"
                          onClick={() => navigate("/appointments")}
                        >
                          Schedule Test
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="bg-blue-50 border border-blue-100 rounded-xl p-4 text-sm text-blue-800">
                    <h4 className="font-medium mb-2">How to Read Your Results</h4>
                    <p className="mb-2">
                      Your imaging results are interpreted by board-certified radiologists. Select any result from the list to view detailed findings and recommendations.
                    </p>
                    <p>
                      If you have questions about your results, please contact your referring physician or schedule a follow-up appointment.
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default Results;
