import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Calendar, FileText, ArrowUpRight, UserCircle } from "lucide-react";
import { Ultrasound, XRay, MRI } from "@/components/CustomIcons";
import { supabase } from "@/lib/supabase";
import RescheduleAppointmentDialog from "@/components/RescheduleAppointmentDialog";
import { checkTableExists } from "@/utils/databaseUtils";

interface Appointment {
  id: string;
  type: string;
  date: string;
  time: string;
  doctor: string;
  location: string;
  status: string;
}

interface Result {
  id: string;
  type: string;
  date: string;
  status: string;
  doctor: string;
}

const mockAppointments: Appointment[] = [
  {
    id: "apt-1",
    type: "Ultrasound",
    date: "2025-03-30",
    time: "9:30 AM",
    doctor: "Dr. <PERSON>",
    location: "Main Clinic, Room 305",
    status: "pending",
  },
  {
    id: "apt-2",
    type: "X-Ray",
    date: "2025-04-15",
    time: "2:00 PM",
    doctor: "Dr. <PERSON>",
    location: "Imaging Center, Building B",
    status: "pending",
  },
];

const mockResults: Result[] = [
  {
    id: "res-1",
    type: "Blood Test",
    date: "2025-03-15",
    status: "Ready",
    doctor: "Dr. Sarah Williams",
  },
  {
    id: "res-2",
    type: "MRI",
    date: "2025-02-28",
    status: "Ready",
    doctor: "Dr. Robert Taylor",
  },
];

const Dashboard = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoaded, setIsLoaded] = useState(false);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [results, setResults] = useState<Result[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [useLocalData, setUseLocalData] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    } else {
      setIsLoaded(true);
      
      if (user) {
        fetchUserData();
      }
    }
  }, [isAuthenticated, navigate, user]);

  const fetchUserData = async () => {
    if (!user) return;
    
    setIsLoadingData(true);
    try {
      console.log("Fetching user data for:", user.id);
      
      const tableExists = await checkTableExists("appointments");
      
      console.log("Appointments table exists:", tableExists);
      
      if (!tableExists) {
        setUseLocalData(true);
        setAppointments(mockAppointments);
        console.log("Using mock appointment data since table doesn't exist");
      } else {
        const { data: appointmentsData, error: appointmentsError } = await supabase
          .from('appointments')
          .select('*')
          .eq('user_id', user.id)
          .order('date', { ascending: true });
        
        console.log("Appointments query result:", { data: appointmentsData, error: appointmentsError });
        
        if (appointmentsError) {
          console.error('Error fetching appointments:', appointmentsError);
          
          if (appointmentsError.code === '42P01') {
            setUseLocalData(true);
            setAppointments(mockAppointments);
          } else {
            toast({
              title: "Data fetch error",
              description: "Failed to load your appointments",
              variant: "destructive",
            });
          }
        } else {
          setAppointments(appointmentsData || []);
        }
      }
      
      const { data: resultsData, error: resultsError } = await supabase
        .from('results')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false });
      
      if (resultsError) {
        console.error('Error fetching results:', resultsError);
        
        if (resultsError.code === '42P01') {
          setUseLocalData(true);
          setResults(mockResults);
        } else {
          toast({
            title: "Data fetch error",
            description: "Failed to load your test results",
            variant: "destructive",
          });
        }
      } else {
        setResults(resultsData || []);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      setUseLocalData(true);
      setAppointments(mockAppointments);
      setResults(mockResults);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    toast({
      title: "Logged out",
      description: "You have been successfully logged out.",
    });
    navigate("/");
  };

  const handleRescheduleClick = (appointment: Appointment) => {
    // Don't allow rescheduling of completed appointments
    if (appointment.status === 'completed') {
      toast({
        title: "Cannot reschedule",
        description: "Completed appointments cannot be rescheduled.",
        variant: "destructive",
      });
      return;
    }
    
    setSelectedAppointment(appointment);
    setIsRescheduleDialogOpen(true);
  };

  const getIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'x-ray':
        return <XRay size={18} />;
      case 'ultrasound':
        return <Ultrasound size={18} />;
      case 'mri':
        return <MRI size={18} />;
      default:
        return <Calendar size={18} />;
    }
  };

  const quickActions = [
    {
      title: "Book Appointment",
      icon: <Calendar size={24} />,
      path: "/appointments",
      color: "bg-blue-500",
    },
    {
      title: "View Results",
      icon: <FileText size={24} />,
      path: "/results",
      color: "bg-green-500",
    },
    {
      title: "Update Profile",
      icon: <UserCircle size={24} />,
      path: "/profile",
      color: "bg-purple-500",
    },
  ];

  if (!isLoaded) {
    return null;
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="bg-white border-b border-slate-200 sticky top-0 z-30">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link to="/" className="flex items-center gap-2">
            <div className="w-8 h-8 bg-health-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">C</span>
            </div>
            <span className="text-xl font-semibold text-slate-900">
              Cent'Percent <span className="text-health-600">HealthCare</span>
            </span>
          </Link>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-health-100 rounded-full flex items-center justify-center mr-2">
                <span className="text-health-600 font-semibold">
                  {user?.firstName?.[0] || "U"}
                </span>
              </div>
              <span className="text-sm font-medium text-slate-700 hidden sm:inline-block">
                {user?.firstName} {user?.lastName}
              </span>
            </div>
            <Button variant="outline" size="sm" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 animate-fade-in">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-slate-600">
            Here's an overview of your health appointments and records.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.path}
              className="bg-white border border-slate-200 rounded-xl p-6 flex items-center gap-4 hover:shadow-md transition-all hover-lift"
            >
              <div className={`${action.color} text-white p-3 rounded-lg`}>
                {action.icon}
              </div>
              <div>
                <h3 className="font-medium text-slate-900">{action.title}</h3>
                <p className="text-sm text-slate-500">Quick access</p>
              </div>
              <ArrowUpRight className="ml-auto text-slate-400" size={18} />
            </Link>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white border border-slate-200 rounded-xl p-6 shadow-sm animate-fade-in" style={{ animationDelay: "0.2s" }}>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-slate-900">
                Appointments
              </h2>
              <Link 
                to="/appointments" 
                className="text-health-600 hover:text-health-700 text-sm font-medium flex items-center"
              >
                View All <ArrowUpRight size={14} className="ml-1" />
              </Link>
            </div>

            {isLoadingData ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin h-8 w-8 border-4 border-health-600 border-t-transparent rounded-full"></div>
              </div>
            ) : appointments.length > 0 ? (
              <div className="space-y-4">
                {appointments.map((appointment) => {
                  const isCompleted = appointment.status === 'completed';
                  const isCancelled = appointment.status === 'cancelled';
                  
                  return (
                    <div
                      key={appointment.id}
                      className={`border border-slate-200 rounded-lg p-4 hover:border-health-200 transition-colors ${
                        isCompleted ? "bg-slate-50" : ""
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          isCompleted ? "bg-green-100 text-green-600" :
                          isCancelled ? "bg-red-100 text-red-600" :
                          "bg-health-100 text-health-600"
                        }`}>
                          {getIcon(appointment.type)}
                        </div>
                        <div className="flex-1">
                          <h3 className={`font-medium ${isCompleted ? "text-slate-600" : "text-slate-900"}`}>
                            {appointment.type} Appointment
                          </h3>
                          <p className="text-sm text-slate-600">
                            {appointment.date} at {appointment.time} • {appointment.doctor}
                          </p>
                          <p className="text-xs text-slate-500 mt-1">
                            {appointment.location}
                          </p>
                          <span className={`inline-block text-xs px-2 py-0.5 rounded-full mt-1 ${
                            appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                            appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                          </span>
                        </div>
                        {!isCompleted && !isCancelled && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs border-health-200 text-health-700"
                            onClick={() => handleRescheduleClick(appointment)}
                          >
                            Reschedule
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="mx-auto text-slate-300 mb-2" size={40} />
                <p className="text-slate-600">No appointments</p>
                <Button className="mt-4 bg-health-600 hover:bg-health-700">
                  Book Appointment
                </Button>
              </div>
            )}
          </div>

          <div className="bg-white border border-slate-200 rounded-xl p-6 shadow-sm animate-fade-in" style={{ animationDelay: "0.4s" }}>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-slate-900">
                Recent Results
              </h2>
              <Link 
                to="/results" 
                className="text-health-600 hover:text-health-700 text-sm font-medium flex items-center"
              >
                View All <ArrowUpRight size={14} className="ml-1" />
              </Link>
            </div>

            {isLoadingData ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin h-8 w-8 border-4 border-health-600 border-t-transparent rounded-full"></div>
              </div>
            ) : results.length > 0 ? (
              <div className="space-y-4">
                {results.map((result) => (
                  <div
                    key={result.id}
                    className="border border-slate-200 rounded-lg p-4 hover:border-health-200 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-green-100 text-green-600 p-2 rounded-full">
                        {getIcon(result.type)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-slate-900">
                          {result.type} Results
                        </h3>
                        <p className="text-sm text-slate-600">
                          {result.date} • {result.doctor}
                        </p>
                        <div className="flex items-center mt-1">
                          <span className={`inline-block ${
                            result.status === 'Ready' ? 'bg-green-100 text-green-800' : 
                            result.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-blue-100 text-blue-800'
                          } text-xs px-2 py-0.5 rounded-full`}>
                            {result.status}
                          </span>
                        </div>
                      </div>
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="text-xs border-health-200 text-health-700"
                      >
                        <Link to={`/results/${result.id}`}>View</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="mx-auto text-slate-300 mb-2" size={40} />
                <p className="text-slate-600">No recent results</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedAppointment && (
        <RescheduleAppointmentDialog
          appointment={selectedAppointment}
          open={isRescheduleDialogOpen}
          onOpenChange={setIsRescheduleDialogOpen}
          onAppointmentUpdated={fetchUserData}
        />
      )}
    </div>
  );
};

export default Dashboard;
