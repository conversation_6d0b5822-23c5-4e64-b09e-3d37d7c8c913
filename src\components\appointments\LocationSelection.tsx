
import { MapPin } from "lucide-react";

interface Location {
  id: string;
  name: string;
  address: string;
}

interface LocationSelectionProps {
  locations: Location[];
  selectedLocation: string;
  onLocationSelect: (locationId: string) => void;
}

const LocationSelection = ({ 
  locations, 
  selectedLocation, 
  onLocationSelect 
}: LocationSelectionProps) => {
  return (
    <div className="space-y-6 animate-fade-in">
      <h2 className="text-xl font-semibold text-slate-900 mb-4">
        Select a Location
      </h2>
      <div className="grid grid-cols-1 gap-4">
        {locations.map((loc) => (
          <button
            key={loc.id}
            type="button"
            className={`p-4 rounded-lg border-2 text-left transition-all ${
              selectedLocation === loc.id
                ? "border-health-600 bg-health-50"
                : "border-slate-200 hover:border-health-200"
            }`}
            onClick={() => onLocationSelect(loc.id)}
          >
            <div className="flex items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mr-4 ${
                  selectedLocation === loc.id
                    ? "bg-health-600 text-white"
                    : "bg-slate-100 text-slate-500"
                }`}
              >
                <MapPin size={18} />
              </div>
              <div>
                <h3 className="font-medium text-slate-900">
                  {loc.name}
                </h3>
                <p className="text-sm text-slate-500">
                  {loc.address}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default LocationSelection;
