
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import AuthForm from "@/components/AuthForm";
import { useToast } from "@/components/ui/use-toast";

const Login = () => {
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  
  // Get the redirect path from location state (if available)
  const returnTo = location.state?.returnTo || "/dashboard";

  // If already authenticated, redirect
  useEffect(() => {
    if (isAuthenticated) {
      navigate(returnTo);
    }
  }, [isAuthenticated, navigate, returnTo]);

  const handleSubmit = async (data: any) => {
    setLoading(true);
    try {
      // Use the login function from AuthContext which uses Supabase
      await login(data.email, data.password);
      
      toast({
        title: "Welcome back!",
        description: "You have successfully logged in.",
      });
      
      // Redirect to the return path or dashboard
      navigate(returnTo);
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Please check your email and password.",
        variant: "destructive",
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-health-50/50 to-white flex flex-col justify-center">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto bg-white p-8 rounded-xl shadow-sm border border-slate-200">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-slate-900 mb-2">Welcome Back</h1>
            <p className="text-slate-600">Sign in to access your health records and appointments</p>
            {returnTo !== "/dashboard" && (
              <p className="text-sm text-health-600 mt-2">You'll be redirected to complete your task after login</p>
            )}
          </div>

          <AuthForm type="login" onSubmit={handleSubmit} loading={loading} />
        </div>
      </div>
    </div>
  );
};

export default Login;
