
import { CalendarIcon, ClockIcon, MailIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface DateTimeSelectionProps {
  date: string;
  time: string;
  email: string;
  notes: string;
  timeSlots: string[];
  onDateChange: (date: string) => void;
  onTimeChange: (time: string) => void;
  onEmailChange: (email: string) => void;
  onNotesChange: (notes: string) => void;
}

const DateTimeSelection = ({
  date,
  time,
  email,
  notes,
  timeSlots,
  onDateChange,
  onTimeChange,
  onEmailChange,
  onNotesChange
}: DateTimeSelectionProps) => {
  return (
    <div className="space-y-6 animate-fade-in">
      <h2 className="text-xl font-semibold text-slate-900 mb-4">
        Select Date & Time
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="date" className="flex items-center mb-2">
            <CalendarIcon size={16} className="mr-2" /> Date
          </Label>
          <Input
            id="date"
            type="date"
            value={date}
            onChange={(e) => onDateChange(e.target.value)}
            min={new Date().toISOString().split("T")[0]}
            className="border-slate-200"
          />
        </div>
        <div>
          <Label htmlFor="time" className="flex items-center mb-2">
            <ClockIcon size={16} className="mr-2" /> Time
          </Label>
          <Select
            value={time}
            onValueChange={onTimeChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select time" />
            </SelectTrigger>
            <SelectContent>
              {timeSlots.map((slot) => (
                <SelectItem key={slot} value={slot}>{slot}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="email" className="flex items-center mb-2">
          <MailIcon size={16} className="mr-2" /> Email for Confirmation
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => onEmailChange(e.target.value)}
          placeholder="<EMAIL>"
          className="border-slate-200"
          required
        />
      </div>

      <div>
        <Label htmlFor="notes" className="mb-2">
          Additional Notes (Optional)
        </Label>
        <textarea
          id="notes"
          value={notes}
          onChange={(e) => onNotesChange(e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-slate-300 rounded-md"
          placeholder="Any special requirements or information you'd like to share"
        ></textarea>
      </div>
    </div>
  );
};

export default DateTimeSelection;
