
import { useState } from "react";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";
import { Appointment } from "@/components/appointments/AppointmentsList";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  AlertDialog,
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog";

interface RescheduleAppointmentDialogProps {
  appointment: Appointment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAppointmentUpdated: () => void;
}

const timeSlots = [
  "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM",
  "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM"
];

const RescheduleAppointmentDialog = ({
  appointment,
  open,
  onOpenChange,
  onAppointmentUpdated
}: RescheduleAppointmentDialogProps) => {
  const { toast } = useToast();
  const [date, setDate] = useState(appointment.date);
  const [time, setTime] = useState(appointment.time);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  
  // Check if appointment is completed and should not be reschedulable
  const isCompleted = appointment.status === 'completed';
  
  useState(() => {
    if (appointment) {
      setDate(appointment.date);
      setTime(appointment.time);
    }
  });
  
  const handleReschedule = async () => {
    if (isCompleted) {
      toast({
        title: "Cannot reschedule",
        description: "Completed appointments cannot be rescheduled.",
        variant: "destructive",
      });
      return;
    }

    if (!date || !time) {
      toast({
        title: "Missing information",
        description: "Please select both date and time",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      console.log(`Rescheduling appointment ${appointment.id} to ${date} at ${time}`);
      
      const { error } = await supabase
        .from('appointments')
        .update({
          date,
          time
        })
        .eq('id', appointment.id);

      if (error) {
        throw error;
      }

      toast({
        title: "Appointment rescheduled",
        description: `Your ${appointment.type} appointment has been rescheduled to ${format(new Date(date), "MMMM d, yyyy")} at ${time}.`,
      });
      
      onAppointmentUpdated();
      
      onOpenChange(false);
    } catch (error) {
      console.error("Error rescheduling appointment:", error);
      toast({
        title: "Error",
        description: "Failed to reschedule your appointment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelAppointment = async () => {
    if (isCompleted) {
      toast({
        title: "Cannot cancel",
        description: "Completed appointments cannot be cancelled.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      console.log(`Cancelling appointment ${appointment.id}`);
      
      // Update appointment status to cancelled instead of deleting
      const { error } = await supabase
        .from('appointments')
        .update({ status: 'cancelled' })
        .eq('id', appointment.id);

      if (error) {
        throw error;
      }

      toast({
        title: "Appointment cancelled",
        description: `Your ${appointment.type} appointment has been cancelled.`,
      });
      
      setShowCancelDialog(false);
      
      onAppointmentUpdated();
      
      onOpenChange(false);
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      toast({
        title: "Error",
        description: "Failed to cancel your appointment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't show dialog for completed appointments
  if (isCompleted) {
    return null;
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reschedule Appointment</DialogTitle>
            <DialogDescription>
              Update the date and time for your {appointment.type} appointment.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="date">New Date</Label>
              <div className="flex items-center">
                <CalendarIcon className="mr-2 h-4 w-4 opacity-50" />
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  min={new Date().toISOString().split("T")[0]}
                  className="border-slate-200"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="time">New Time</Label>
              <Select value={time} onValueChange={setTime}>
                <SelectTrigger id="time">
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((slot) => (
                    <SelectItem key={slot} value={slot}>{slot}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="pt-2">
              <p className="text-sm text-slate-500">
                Current appointment: {format(new Date(appointment.date), "MMMM d, yyyy")} at {appointment.time}
              </p>
            </div>
          </div>
          <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between gap-2">
            <Button 
              variant="outline" 
              onClick={() => setShowCancelDialog(true)}
              className="w-full sm:w-auto"
            >
              Cancel Appointment
            </Button>
            <Button 
              onClick={handleReschedule}
              disabled={isSubmitting}
              className="w-full sm:w-auto bg-health-600 hover:bg-health-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Confirm Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will cancel your {appointment.type} appointment on{" "}
              {format(new Date(appointment.date), "MMMM d, yyyy")} at {appointment.time}.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Keep Appointment</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelAppointment}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Cancelling...
                </>
              ) : (
                "Yes, Cancel Appointment"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RescheduleAppointmentDialog;
