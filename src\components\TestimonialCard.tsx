
import { useState } from "react";

interface TestimonialCardProps {
  content: string;
  author: string;
  role: string;
  avatarUrl?: string;
  rating: number;
}

const TestimonialCard = ({
  content,
  author,
  role,
  avatarUrl,
  rating,
}: TestimonialCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`bg-white p-6 rounded-xl border border-slate-200 transition-all duration-300 ${
        isHovered 
          ? "shadow-lg transform -translate-y-1 border-health-200" 
          : "shadow-sm"
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`w-5 h-5 ${
              i < rating ? "text-yellow-400" : "text-slate-300"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        ))}
      </div>

      <div className="relative mb-6">
        <svg
          className="absolute top-0 left-0 transform -translate-x-4 -translate-y-6 h-8 w-8 text-health-200"
          fill="currentColor"
          viewBox="0 0 32 32"
          aria-hidden="true"
        >
          <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
        </svg>
        <p className="text-slate-700 relative z-10">{content}</p>
      </div>

      <div className="flex items-center">
        <div className="flex-shrink-0 mr-3">
          {avatarUrl ? (
            <img
              className="h-10 w-10 rounded-full object-cover"
              src={avatarUrl}
              alt={author}
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-health-100 flex items-center justify-center">
              <span className="text-health-600 font-medium text-sm">
                {author.split(" ").map(n => n[0]).join("")}
              </span>
            </div>
          )}
        </div>
        <div>
          <h4 className="text-sm font-medium text-slate-900">{author}</h4>
          <p className="text-xs text-slate-500">{role}</p>
        </div>
      </div>
    </div>
  );
};

export default TestimonialCard;
