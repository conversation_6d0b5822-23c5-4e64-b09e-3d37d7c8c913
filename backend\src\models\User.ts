import { Pool } from 'pg';

// Database row interface - matches the database schema exactly
export interface UserRow {
  id: string;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  phone_number: string | null;
  created_at: Date;
  updated_at: Date;
}

// Public user interface - excludes sensitive data like password_hash
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// User creation interface - for registration
export interface CreateUserData {
  email: string;
  password: string; // Plain text password (will be hashed)
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

// User update interface - for profile updates
export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
}

// Login credentials interface
export interface LoginCredentials {
  email: string;
  password: string;
}

// JWT payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  iat?: number;
  exp?: number;
}

// User model class for database operations
export class UserModel {
  private pool: Pool;

  constructor(pool: Pool) {
    this.pool = pool;
  }

  // Convert database row to public User interface
  private mapRowToUser(row: UserRow): User {
    return {
      id: row.id,
      email: row.email,
      firstName: row.first_name,
      lastName: row.last_name,
      phoneNumber: row.phone_number,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  // Find user by ID
  async findById(id: string): Promise<User | null> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'SELECT * FROM users WHERE id = $1',
        [id]
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return this.mapRowToUser(result.rows[0]);
    } finally {
      client.release();
    }
  }

  // Find user by email
  async findByEmail(email: string): Promise<User | null> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'SELECT * FROM users WHERE email = $1',
        [email.toLowerCase()]
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return this.mapRowToUser(result.rows[0]);
    } finally {
      client.release();
    }
  }

  // Find user by email with password hash (for authentication)
  async findByEmailWithPassword(email: string): Promise<UserRow | null> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'SELECT * FROM users WHERE email = $1',
        [email.toLowerCase()]
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0];
    } finally {
      client.release();
    }
  }

  // Create new user
  async create(userData: CreateUserData, passwordHash: string): Promise<User> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone_number)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [
          userData.email.toLowerCase(),
          passwordHash,
          userData.firstName,
          userData.lastName,
          userData.phoneNumber || null,
        ]
      );
      
      return this.mapRowToUser(result.rows[0]);
    } finally {
      client.release();
    }
  }

  // Update user
  async update(id: string, userData: UpdateUserData): Promise<User | null> {
    const client = await this.pool.connect();
    try {
      const setParts: string[] = [];
      const values: any[] = [];
      let paramCount = 1;

      if (userData.email !== undefined) {
        setParts.push(`email = $${paramCount}`);
        values.push(userData.email.toLowerCase());
        paramCount++;
      }

      if (userData.firstName !== undefined) {
        setParts.push(`first_name = $${paramCount}`);
        values.push(userData.firstName);
        paramCount++;
      }

      if (userData.lastName !== undefined) {
        setParts.push(`last_name = $${paramCount}`);
        values.push(userData.lastName);
        paramCount++;
      }

      if (userData.phoneNumber !== undefined) {
        setParts.push(`phone_number = $${paramCount}`);
        values.push(userData.phoneNumber);
        paramCount++;
      }

      if (setParts.length === 0) {
        // No updates to make
        return this.findById(id);
      }

      // Add updated_at
      setParts.push(`updated_at = NOW()`);
      values.push(id);

      const query = `
        UPDATE users 
        SET ${setParts.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `;

      const result = await client.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return this.mapRowToUser(result.rows[0]);
    } finally {
      client.release();
    }
  }

  // Delete user
  async delete(id: string): Promise<boolean> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'DELETE FROM users WHERE id = $1',
        [id]
      );
      
      return result.rowCount > 0;
    } finally {
      client.release();
    }
  }

  // Check if email exists
  async emailExists(email: string): Promise<boolean> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'SELECT 1 FROM users WHERE email = $1',
        [email.toLowerCase()]
      );
      
      return result.rows.length > 0;
    } finally {
      client.release();
    }
  }

  // Get user count (for admin purposes)
  async getUserCount(): Promise<number> {
    const client = await this.pool.connect();
    try {
      const result = await client.query('SELECT COUNT(*) as count FROM users');
      return parseInt(result.rows[0].count);
    } finally {
      client.release();
    }
  }
}
