
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Security measure: Detect iframe embedding with more flexible approach
if (window.top !== window.self) {
  const allowedDomains = [
    'lovable.dev', 
    'lovableproject.com', 
    'localhost', 
    '127.0.0.1'
  ];

  const referrer = document.referrer;
  const isAllowedDomain = allowedDomains.some(domain => 
    referrer.includes(domain)
  );

  if (!isAllowedDomain) {
    document.body.innerHTML = 'Access denied: This application cannot be loaded in an iframe.';
    throw new Error('Security violation: Application loaded in an unverified iframe');
  }
}

// Security measure: Basic runtime threat detection
window.addEventListener('error', (event) => {
  console.error('Runtime error detected:', event.message);
  // You could implement additional measures here
});

// Security measure: Monitor storage access
const originalSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
  console.log(`LocalStorage write: ${key}`);
  // You could add validation logic here
  return originalSetItem.call(this, key, value);
};

// Initialize the application
createRoot(document.getElementById("root")!).render(<App />);

