
import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Users, Heart, Handshake, Hospital, ArrowDown, ArrowUp } from "lucide-react";

const About = () => {
  const [activeSection, setActiveSection] = useState<string>("story");
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setActiveSection(id);
    }
  };

  return (
    <>
      <Navbar />
      
      <main className="pt-24 pb-12">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-health-50 to-health-100 py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-block bg-health-200 text-health-700 px-4 py-1 rounded-full text-sm font-medium mb-3">
                Our Story
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
                Making Healthcare Accessible For Everyone
              </h1>
              <p className="text-xl text-slate-700 mb-8">
                At Cent'Percent HealthCare, we're driven by a profound mission: to ensure quality diagnostic services reach every corner of the world, regardless of geography or economic status.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button
                  onClick={() => scrollToSection("story")}
                  className={`${activeSection === "story" ? "bg-health-600" : "bg-health-500"} hover:bg-health-700 text-white`}
                >
                  Our Founder's Story
                </Button>
                <Button
                  onClick={() => scrollToSection("mission")}
                  variant="outline"
                  className={`border-health-200 ${activeSection === "mission" ? "text-health-800 bg-health-100" : "text-health-700"}`}
                >
                  Mission & Values
                </Button>
                <Button
                  onClick={() => scrollToSection("team")}
                  variant="outline"
                  className={`border-health-200 ${activeSection === "team" ? "text-health-800 bg-health-100" : "text-health-700"}`}
                >
                  Our Team
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Founder's Story Section */}
        <section id="story" className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-20'}`}>
                <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                  The Journey Behind Cent'Percent
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                  A Personal Mission Born from Experience
                </h2>
                <div className="prose prose-lg max-w-none text-slate-600">
                  <p>
                    Dr. A K Sao still vividly remembers the night his grandmother fell ill in their small rural community of Pinehaven. The nearest diagnostic center was over 100 miles away, and his family had to pool together their savings just to afford the tests she urgently needed.
                  </p>
                  <p>
                    "I was only twelve years old," Dr. Sao recalls, "but I remember the fear in my parents' eyes—not just about my grandmother's health, but about how we would afford the care she needed. We had to wait three days to make the journey, three days where her condition only worsened."
                  </p>
                  <p>
                    After that experience, A K committed himself to changing the system. Through scholarships and working multiple jobs, he put himself through medical school, specializing in radiology. But he never forgot his family's struggle, or the struggles of countless others in rural communities like his.
                  </p>
                  <p>
                    "When I completed my medical training, I saw that even in urban areas, quality diagnostic services remained prohibitively expensive for many families. The same barriers I witnessed as a child were still preventing proper healthcare access decades later."
                  </p>
                  <p>
                    In 2012, Dr. Sao opened the first Cent'Percent HealthCare center with a simple mission: to provide 100% quality care while making it accessible to 100% of people. The name reflected his commitment—not 99%, but cent'percent—everyone deserves access, no exceptions.
                  </p>
                  <p>
                    "Our sliding-scale payment model, rural outreach programs, and teleradiology services are all designed to break down the barriers I witnessed firsthand. This isn't just a business for me—it's the fulfillment of a promise I made to my grandmother before she passed, that no one would have to choose between financial security and necessary healthcare."
                  </p>
                </div>
              </div>
              
              <div className={`relative transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-20'}`}>
                <div className="relative">
                  <div className="absolute -inset-4 bg-health-200 rounded-2xl transform rotate-2 opacity-30"></div>
                  <img
                    src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&q=80&w=800&h=500"
                    alt="Doctor with patient"
                    className="rounded-xl shadow-lg relative z-10"
                    loading="lazy"
                  />
                  <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg animate-fade-in" style={{ animationDelay: "0.6s" }}>
                    <div className="flex items-center gap-3">
                      <div className="bg-health-100 p-2 rounded-full">
                        <Heart className="w-5 h-5 text-health-600" />
                      </div>
                      <div>
                        <p className="text-slate-900 font-medium">Our Promise</p>
                        <p className="text-xs text-slate-600">Quality care for everyone</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="rounded-lg overflow-hidden shadow-md">
                    <img
                      src="https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?auto=format&fit=crop&q=80&w=400&h=300"
                      alt="Rural healthcare facility"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                  <div className="rounded-lg overflow-hidden shadow-md">
                    <img
                      src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&q=80&w=400&h=300"
                      alt="Modern lab equipment"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission and Values Section */}
        <section id="mission" className="py-20 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                Our Purpose
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Mission & Core Values
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Our mission and values guide every decision we make, from patient care to business operations.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
              <Card className="border-health-100 shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="bg-health-100 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                    <ArrowUp className="text-health-600 w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 mb-3">Our Mission</h3>
                  <p className="text-slate-600">
                    To make laboratory tests and diagnostic services affordable and accessible to everyone, 
                    regardless of their location or economic status. We are committed to breaking down 
                    financial and geographical barriers that prevent people from receiving the diagnostic care 
                    they need for better health outcomes.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-health-100 shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="bg-health-100 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                    <ArrowDown className="text-health-600 w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 mb-3">Our Vision</h3>
                  <p className="text-slate-600">
                    A world where no one has to forgo essential laboratory tests due to cost or distance. We envision 
                    a healthcare system where affordable and quality diagnostic services are within reach of every 
                    individual, enabling early detection, better treatment decisions, and improved health outcomes for all.
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  icon: <Users className="text-health-600 w-6 h-6" />,
                  title: "Patient-Centered",
                  description: "Every decision we make puts patients first, ensuring dignity, comfort, and respect at every touchpoint."
                },
                {
                  icon: <Heart className="text-health-600 w-6 h-6" />,
                  title: "Affordability",
                  description: "We believe quality laboratory testing should not be a luxury. We continuously work to make our services affordable for all income levels."
                },
                {
                  icon: <Handshake className="text-health-600 w-6 h-6" />,
                  title: "Accessibility",
                  description: "We eliminate barriers to care through innovative payment models, mobile services, and outreach programs to underserved communities."
                },
                {
                  icon: <Hospital className="text-health-600 w-6 h-6" />,
                  title: "Excellence",
                  description: "We maintain the highest standards in every aspect of our practice, from technical accuracy to patient experience."
                },
              ].map((value, index) => (
                <Card key={index} className="border-health-100 shadow-sm hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="bg-health-50 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                      {value.icon}
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 mb-2">{value.title}</h3>
                    <p className="text-slate-600 text-sm">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Our Team Section */}
        <section id="team" className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                The People Behind Our Mission
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Meet Our Leadership Team
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Our expert team combines clinical excellence with a shared commitment to making healthcare accessible to all.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "Dr. A K Sao",
                  role: "Founder & Medical Director",
                  image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Board-certified radiologist with a special interest in rural healthcare access. Dr. Sao leads our clinical excellence initiatives."
                },
                {
                  name: "Dr. Maya Patel",
                  role: "Chief Radiologist",
                  image: "https://images.unsplash.com/photo-1594824476967-48c8b964273f?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Specialist in diagnostic imaging with over 15 years of experience in MRI and CT scan interpretation, focused on patient-centered care."
                },
                {
                  name: "Alex Martinez",
                  role: "Operations Director",
                  image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Expert in healthcare operations with a background in public health, leading our initiatives to optimize patient experience."
                },
                {
                  name: "Dr. Sarah Johnson",
                  role: "Laboratory Director",
                  image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Clinical pathologist overseeing our laboratory services with a focus on accuracy, efficiency, and expanding our test portfolio."
                },
                {
                  name: "Michael Chen",
                  role: "Technology & Innovation Lead",
                  image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Healthcare IT specialist leading our teleradiology platform and digital accessibility initiatives for remote communities."
                },
                {
                  name: "Lisa Washington",
                  role: "Community Outreach Director",
                  image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&q=80&w=300&h=300",
                  bio: "Public health advocate coordinating our mobile diagnostic units and community screening programs in underserved areas."
                },
              ].map((member, index) => (
                <Card key={index} className="border-health-100 overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                      loading="lazy"
                    />
                  </div>
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-bold text-slate-900">{member.name}</h3>
                    <p className="text-health-600 font-medium mb-2">{member.role}</p>
                    <p className="text-slate-600 text-sm">
                      {member.bio}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Initiatives Section */}
        <section className="py-20 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-block bg-health-100 text-health-600 px-4 py-1 rounded-full text-sm font-medium mb-3">
                Making a Difference
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Our Initiatives
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Learn about the programs we've created to make laboratory tests and diagnostic healthcare more affordable and accessible for everyone.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  title: "Mobile Diagnostic Units",
                  description: "Our fleet of mobile diagnostic units brings laboratory testing services directly to underserved communities, serving over 50 rural locations monthly.",
                  image: "https://images.unsplash.com/photo-1516549655169-df83a0774514?auto=format&fit=crop&q=80&w=600&h=400"
                },
                {
                  title: "Sliding-Scale Payment Program",
                  description: "Our payment program adjusts test costs based on income, ensuring essential laboratory services remain affordable regardless of financial circumstances.",
                  image: "https://images.unsplash.com/photo-1589553416052-46e143de7b1c?auto=format&fit=crop&q=80&w=600&h=400"
                },
                {
                  title: "Community Health Camps",
                  description: "Regular health camps in underserved areas provide free basic health screenings and subsidized comprehensive laboratory testing for early disease detection.",
                  image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&q=80&w=600&h=400"
                },
              ].map((initiative, index) => (
                <div key={index} className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <img
                    src={initiative.image}
                    alt={initiative.title}
                    className="w-full h-48 object-cover"
                    loading="lazy"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-slate-900 mb-2">{initiative.title}</h3>
                    <p className="text-slate-600">
                      {initiative.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-health-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2">
                <div className="p-8 md:p-12 flex flex-col justify-center">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Join Our Mission
                  </h2>
                  <p className="text-slate-600 mb-8">
                    Whether you're a patient seeking care, a healthcare professional looking to make a difference, or a supporter of our mission, we'd love to connect with you.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      asChild
                      className="bg-health-600 hover:bg-health-700 text-white neo-button"
                    >
                      <Link to="/contact">Contact Us</Link>
                    </Button>
                    <Button
                      asChild
                      variant="outline"
                      className="border-health-200 text-health-700"
                    >
                      <Link to="/services">Explore Our Services</Link>
                    </Button>
                  </div>
                </div>
                <div className="relative h-64 md:h-auto">
                  <img
                    src="https://images.unsplash.com/photo-1576765608866-5b51046452be?auto=format&fit=crop&q=80&w=600"
                    alt="Healthcare team meeting"
                    className="absolute inset-0 w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-l from-transparent to-health-900/30"></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default About;
