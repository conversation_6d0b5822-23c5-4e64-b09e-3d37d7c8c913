import { Pool } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'centpercent_care',
  user: process.env.DB_USER || 'centpercent_app',
  password: process.env.DB_PASSWORD || 'your_password',
});

async function testConnection() {
  try {
    console.log('🔄 Testing PostgreSQL connection...');
    
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Successfully connected to PostgreSQL!');
    
    // Test database query
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('📅 Current time:', result.rows[0].current_time);
    console.log('🐘 PostgreSQL version:', result.rows[0].pg_version);
    
    // Test database exists
    const dbCheck = await client.query(
      "SELECT datname FROM pg_database WHERE datname = $1",
      [process.env.DB_NAME || 'centpercent_care']
    );
    
    if (dbCheck.rows.length > 0) {
      console.log('✅ Database exists:', dbCheck.rows[0].datname);
    } else {
      console.log('❌ Database does not exist');
    }
    
    client.release();
    console.log('🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error details:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('password authentication failed')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Check your database password in .env file');
        console.log('2. Verify the user exists in PostgreSQL');
        console.log('3. Make sure the user has login permissions');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Create the database in pgAdmin');
        console.log('2. Check the database name in .env file');
      } else if (error.message.includes('connect ECONNREFUSED')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Make sure PostgreSQL service is running');
        console.log('2. Check if the port 5432 is correct');
        console.log('3. Verify the host is localhost');
      }
    }
  } finally {
    await pool.end();
  }
}

// Run the test
testConnection();
