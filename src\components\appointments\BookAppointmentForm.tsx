
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import AppointmentTypeSelection from "./AppointmentTypeSelection";
import LocationSelection from "./LocationSelection";
import DateTimeSelection from "./DateTimeSelection";
import AppointmentConfirmation from "./AppointmentConfirmation";
import { appointmentTypes, locations, timeSlots, categories } from "./appointmentData";
import { getAppointmentTypeById } from "./appointmentUtils";
import { supabase } from "@/lib/supabase";

interface BookAppointmentFormProps {
  userId: string;
  email: string;
  onAppointmentBooked: (confirmationNumber: string) => void;
  onCancel: () => void;
}

const BookAppointmentForm = ({ 
  userId, 
  email: initialEmail, 
  onAppointmentBooked, 
  onCancel 
}: BookAppointmentFormProps) => {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [appointmentType, setAppointmentType] = useState("");
  const [location, setLocation] = useState("");
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [notes, setNotes] = useState("");
  const [email, setEmail] = useState(initialEmail);
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNext = () => {
    if (step === 1 && !appointmentType) {
      toast({
        title: "Please select an appointment type",
        variant: "destructive",
      });
      return;
    }
    
    if (step === 2 && !location) {
      toast({
        title: "Please select a location",
        variant: "destructive",
      });
      return;
    }
    
    if (step === 3 && (!date || !time)) {
      toast({
        title: "Please select both date and time",
        variant: "destructive",
      });
      return;
    }
    
    setStep(step + 1);
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Email is required",
        description: "Please provide an email to receive appointment details",
        variant: "destructive",
      });
      return;
    }
    
    const newConfirmationNumber = `APT-${Math.random().toString(36).substring(2, 9).toUpperCase()}`;
    
    setIsSubmitting(true);
    
    try {
      const selectedType = getAppointmentTypeById(appointmentType);
      const selectedLocation = locations.find(loc => loc.id === location);
      
      console.log("Saving appointment for user:", userId);
      
      // Create the appointment data
      const appointmentData = {
        user_id: userId,
        type: selectedType?.name || "",
        date: date,
        time: time,
        doctor: "Dr. " + ["Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller"][Math.floor(Math.random() * 7)],
        location: selectedLocation?.name || "",
        notes: notes || null,
        confirmation_number: newConfirmationNumber
      };
      
      console.log("Appointment data to save:", appointmentData);
      
      const { data, error } = await supabase
        .from('appointments')
        .insert([appointmentData])
        .select();
          
      if (error) {
        console.error('Detailed Supabase error:', error);
        
        // Check if it's a permissions or structure error
        if (error.code === '42501') {
          console.error('Permission denied. Check RLS policies.');
        } else if (error.code === '42P01') {
          console.error('Table does not exist. Database setup may be incomplete.');
        }
        
        toast({
          title: "Error saving appointment",
          description: error.message || "Your appointment details couldn't be saved. Please try again.",
          variant: "destructive",
        });
        throw error;
      }
      
      console.log("Appointment saved successfully:", data);
      
      toast({
        title: "Appointment booked!",
        description: "Your appointment has been successfully scheduled. Details have been sent to your email.",
      });
      
      onAppointmentBooked(newConfirmationNumber);
      
    } catch (error) {
      console.error('Comprehensive booking error:', error);
      toast({
        title: "Booking failed",
        description: "There was an unexpected error booking your appointment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedAppointmentType = () => {
    return appointmentTypes.find(type => type.id === appointmentType);
  };

  const getSelectedLocation = () => {
    return locations.find(loc => loc.id === location);
  };

  return (
    <>
      <div className="flex items-center mb-6">
        <Button
          variant="outline"
          onClick={onCancel}
          className="mr-4"
        >
          ← Back to Appointments
        </Button>
        <h1 className="text-3xl font-bold text-slate-900 text-center flex-1">
          Schedule an Appointment
        </h1>
      </div>
      <p className="text-slate-600 mb-8 text-center">
        Book your healthcare service with our state-of-the-art equipment and expert staff
      </p>

      <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
        <div className="p-6 border-b border-slate-200">
          <div className="flex justify-between items-center mb-6">
            {[1, 2, 3, 4].map((stepNumber) => (
              <div
                key={stepNumber}
                className={`flex items-center ${
                  stepNumber < step
                    ? "text-health-600"
                    : stepNumber === step
                    ? "text-health-600"
                    : "text-slate-400"
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                    stepNumber < step
                      ? "bg-health-600 text-white"
                      : stepNumber === step
                      ? "bg-health-100 text-health-600 border-2 border-health-600"
                      : "bg-slate-100 text-slate-400 border border-slate-200"
                  }`}
                >
                  {stepNumber < step ? "✓" : stepNumber}
                </div>
                <span className="text-sm font-medium hidden sm:inline">
                  {stepNumber === 1
                    ? "Service"
                    : stepNumber === 2
                    ? "Location"
                    : stepNumber === 3
                    ? "Date & Time"
                    : "Confirm"}
                </span>
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {step === 1 && (
              <AppointmentTypeSelection
                appointmentTypes={appointmentTypes}
                categories={categories}
                selectedCategory={selectedCategory}
                appointmentType={appointmentType}
                onCategoryChange={setSelectedCategory}
                onTypeChange={setAppointmentType}
              />
            )}

            {step === 2 && (
              <LocationSelection
                locations={locations}
                selectedLocation={location}
                onLocationSelect={setLocation}
              />
            )}

            {step === 3 && (
              <DateTimeSelection
                date={date}
                time={time}
                email={email}
                notes={notes}
                timeSlots={timeSlots}
                onDateChange={setDate}
                onTimeChange={setTime}
                onEmailChange={setEmail}
                onNotesChange={setNotes}
              />
            )}

            {step === 4 && (
              <AppointmentConfirmation
                appointmentTypeName={getSelectedAppointmentType()?.name || ""}
                appointmentTypeIcon={getSelectedAppointmentType()?.icon || null}
                locationName={getSelectedLocation()?.name || ""}
                locationAddress={getSelectedLocation()?.address || ""}
                date={date}
                time={time}
                email={email}
                notes={notes}
              />
            )}
          </div>

          <div className="p-6 bg-slate-50 border-t border-slate-200 flex flex-col sm:flex-row-reverse sm:justify-between gap-3">
            {step < 4 ? (
              <Button
                type="button"
                onClick={handleNext}
                className="bg-health-600 hover:bg-health-700 text-white w-full sm:w-auto neo-button"
              >
                Continue
              </Button>
            ) : (
              <Button
                type="submit"
                className="bg-health-600 hover:bg-health-700 text-white w-full sm:w-auto neo-button"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Booking..." : "Confirm Appointment"}
              </Button>
            )}
            {step > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                className="border-slate-200 w-full sm:w-auto"
              >
                Back
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
};

export default BookAppointmentForm;
