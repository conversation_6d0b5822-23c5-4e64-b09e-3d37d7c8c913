
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";
import { Navbar } from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import RescheduleAppointmentDialog from "@/components/RescheduleAppointmentDialog";

// Import refactored components
import EmptyAppointments from "@/components/appointments/EmptyAppointments";
import AppointmentsList, { Appointment } from "@/components/appointments/AppointmentsList";
import BookAppointmentForm from "@/components/appointments/BookAppointmentForm";
import AppointmentBookingComplete from "@/components/appointments/AppointmentBookingComplete";
import AppointmentCancelDialog from "@/components/appointments/AppointmentCancelDialog";
import { getAppointmentTypeIcon } from "@/components/appointments/appointmentUtils";
import { appointmentTypes, locations } from "@/components/appointments/appointmentData";
import { checkTableExists } from "@/utils/databaseUtils";

const Appointments = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [email, setEmail] = useState("");
  const [schedulingMode, setSchedulingMode] = useState(false);
  const [bookingComplete, setBookingComplete] = useState(false);
  const [confirmationNumber, setConfirmationNumber] = useState("");
  const [savedAppointments, setSavedAppointments] = useState<Appointment[]>([]);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showRescheduleDialog, setShowRescheduleDialog] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [appointmentToCancel, setAppointmentToCancel] = useState<Appointment | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);
  
  const selectedAppointmentType = appointmentTypes.find(
    type => selectedAppointment && type.name === selectedAppointment.type
  );

  const selectedLocation = locations.find(
    loc => selectedAppointment && loc.name === selectedAppointment.location
  );

  useEffect(() => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication required",
        description: "Please log in to book appointments",
        variant: "destructive",
      });
      navigate("/login", { state: { returnTo: "/appointments" } });
    } else if (user?.id) {
      setEmail(user.email || "");
      fetchUserAppointments();
    }
  }, [isAuthenticated, navigate, user, toast]);
  
  const fetchUserAppointments = async () => {
    if (!user?.id) return;
    
    try {
      console.log("Fetching appointments for user ID:", user.id);
      
      const tableExists = await checkTableExists("appointments");
      console.log("Appointments table exists:", tableExists);
      
      if (!tableExists) {
        console.warn("Appointments table does not exist - using mock data");
        setSavedAppointments([]);
        return;
      }
      
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: true });
        
      if (error) {
        console.error('Error fetching appointments:', error);
        toast({
          title: "Error",
          description: "Could not load your appointments. Please try again later.",
          variant: "destructive",
        });
      } else {
        console.log("Appointments fetched:", data);
        setSavedAppointments(data || []);
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Could not load your appointments. Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleReschedule = (appointment: Appointment) => {
    // Don't allow rescheduling of completed appointments
    if (appointment.status === 'completed') {
      toast({
        title: "Cannot reschedule",
        description: "Completed appointments cannot be rescheduled.",
        variant: "destructive",
      });
      return;
    }
    
    setSelectedAppointment(appointment);
    setShowRescheduleDialog(true);
  };

  const handleCancelAppointment = async () => {
    if (!appointmentToCancel) return;

    // Don't allow cancelling completed appointments
    if (appointmentToCancel.status === 'completed') {
      toast({
        title: "Cannot cancel",
        description: "Completed appointments cannot be cancelled.",
        variant: "destructive",
      });
      return;
    }

    setIsCancelling(true);
    try {
      // Update appointment status to cancelled instead of deleting
      const { error } = await supabase
        .from('appointments')
        .update({ status: 'cancelled' })
        .eq('id', appointmentToCancel.id);

      if (error) {
        throw error;
      }

      // Update the appointment in the local state
      setSavedAppointments(prevAppointments => 
        prevAppointments.map(app => 
          app.id === appointmentToCancel.id 
            ? { ...app, status: 'cancelled' }
            : app
        )
      );

      toast({
        title: "Appointment cancelled",
        description: `Your ${appointmentToCancel.type} appointment has been cancelled.`,
      });
      
      setCancelDialogOpen(false);
      setAppointmentToCancel(null);
      
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      toast({
        title: "Error",
        description: "Failed to cancel your appointment. Please try again.",
        variant: "destructive",
      });
      
      fetchUserAppointments();
    } finally {
      setIsCancelling(false);
    }
  };

  const openCancelDialog = (appointment: Appointment) => {
    // Don't allow cancelling completed appointments
    if (appointment.status === 'completed') {
      toast({
        title: "Cannot cancel",
        description: "Completed appointments cannot be cancelled.",
        variant: "destructive",
      });
      return;
    }
    
    setAppointmentToCancel(appointment);
    setCancelDialogOpen(true);
  };

  const handleAppointmentBooked = (confirmationNum: string) => {
    setConfirmationNumber(confirmationNum);
    setBookingComplete(true);
  };

  return (
    <>
      <Navbar />
      <div className="pt-20 pb-16 min-h-screen bg-gradient-to-b from-health-50/50 to-white">
        <div className="container mx-auto px-4 py-8">
          {!schedulingMode && !bookingComplete ? (
            <div className="max-w-4xl mx-auto">
              <div className="flex justify-between items-center mb-8">
                <h1 className="text-3xl font-bold text-slate-900">
                  My Appointments
                </h1>
                <Button 
                  onClick={() => setSchedulingMode(true)}
                  className="bg-health-600 hover:bg-health-700 text-white"
                >
                  Schedule New Appointment
                </Button>
              </div>
              
              {savedAppointments.length === 0 ? (
                <EmptyAppointments onSchedule={() => setSchedulingMode(true)} />
              ) : (
                <AppointmentsList 
                  appointments={savedAppointments}
                  onReschedule={handleReschedule}
                  onCancelDialogOpen={openCancelDialog} 
                />
              )}
            </div>
          ) : (
            <div className="max-w-2xl mx-auto">
              {!schedulingMode && bookingComplete ? (
                <AppointmentBookingComplete
                  confirmationNumber={confirmationNumber}
                  appointmentTypeName={selectedAppointmentType?.name || ""}
                  appointmentTypeIcon={selectedAppointmentType?.icon || null}
                  locationName={selectedLocation?.name || ""}
                  locationAddress={selectedLocation?.address || ""}
                  date={selectedAppointment?.date || ""}
                  time={selectedAppointment?.time || ""}
                  email={email}
                  onViewAppointments={() => {
                    setSchedulingMode(false);
                    setBookingComplete(false);
                    fetchUserAppointments();
                  }}
                />
              ) : (
                <BookAppointmentForm
                  userId={user?.id || ""}
                  email={email}
                  onAppointmentBooked={handleAppointmentBooked}
                  onCancel={() => setSchedulingMode(false)}
                />
              )}
            </div>
          )}
        </div>
      </div>

      {selectedAppointment && (
        <RescheduleAppointmentDialog
          appointment={selectedAppointment}
          open={showRescheduleDialog}
          onOpenChange={(open) => {
            setShowRescheduleDialog(open);
            if (!open) {
              fetchUserAppointments();
            }
          }}
          onAppointmentUpdated={fetchUserAppointments}
        />
      )}
      
      <AppointmentCancelDialog
        isOpen={cancelDialogOpen}
        isCancelling={isCancelling}
        appointment={appointmentToCancel}
        onOpenChange={setCancelDialogOpen}
        onConfirmCancel={handleCancelAppointment}
      />
      
      <Footer />
    </>
  );
};

export default Appointments;
export { Appointments };
