import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { UserCircle, Menu, X, LogOut, MapPin } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { name: "Home", path: "/" },
    { name: "Services", path: "/services" },
    { name: "About", path: "/about" },
    { name: "Contact", path: "/contact" },
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });
      navigate("/");
    } catch (error) {
      console.error("Logout error:", error);
      toast({
        title: "Logout failed",
        description: "An error occurred while logging out. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <header
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "py-2 bg-white/90 backdrop-blur-md shadow-sm"
          : "py-4 bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 flex items-center justify-between">
        <Link 
          to="/" 
          className="flex items-center gap-2 group"
          aria-label="Cent'Percent HealthCare"
        >
          <div className="w-8 h-8 bg-health-600 rounded-full flex items-center justify-center animate-pulse-soft">
            <span className="text-white font-bold">C</span>
          </div>
          <span className={`text-xl font-semibold ${isScrolled ? 'text-health-900' : 'text-health-950'} transition-colors group-hover:text-health-600`}>
            Cent'Percent <span className="text-health-600">HealthCare</span>
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-8">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              className={`hover:text-health-600 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-health-600 ${
                location.pathname === link.path
                  ? "text-health-600 after:w-full"
                  : "text-foreground after:w-0"
              } after:transition-all after:duration-300 hover:after:w-full`}
            >
              {link.name}
            </Link>
          ))}
          {isAuthenticated && (
            <Link
              to="/appointments"
              className={`hover:text-health-600 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-health-600 ${
                location.pathname === "/appointments"
                  ? "text-health-600 after:w-full"
                  : "text-foreground after:w-0"
              } after:transition-all after:duration-300 hover:after:w-full`}
            >
              Appointments
            </Link>
          )}
          {isAuthenticated && (
            <Link
              to="/results"
              className={`hover:text-health-600 transition-colors relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-health-600 ${
                location.pathname === "/results"
                  ? "text-health-600 after:w-full"
                  : "text-foreground after:w-0"
              } after:transition-all after:duration-300 hover:after:w-full`}
            >
              Results
            </Link>
          )}
        </nav>

        {/* Auth Buttons - Desktop */}
        <div className="hidden md:flex items-center gap-4">
          {!isAuthenticated ? (
            <>
              <Link to="/login">
                <Button variant="outline" className="hover-lift">
                  Login
                </Button>
              </Link>
              <Link to="/register">
                <Button className="bg-health-600 hover:bg-health-700 text-white neo-button">
                  Register
                </Button>
              </Link>
            </>
          ) : (
            <div className="flex items-center gap-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative rounded-full w-10 h-10 p-0">
                    <UserCircle className="w-6 h-6" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 z-50 bg-white">
                  <DropdownMenuLabel>
                    {user?.firstName} {user?.lastName}
                  </DropdownMenuLabel>
                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                    {user?.email}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate("/dashboard")}>
                    Dashboard
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate("/appointments")}>
                    My Appointments
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate("/results")}>
                    Test Results
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Mobile menu button */}
        <button
          onClick={toggleMobileMenu}
          className="md:hidden text-foreground"
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile menu */}
      <div
        className={`absolute top-full left-0 w-full bg-white/95 backdrop-blur-md shadow-md transition-all duration-300 overflow-hidden md:hidden ${
          mobileMenuOpen ? "max-h-screen py-4" : "max-h-0"
        }`}
      >
        <div className="container mx-auto px-4 flex flex-col gap-4">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              className={`py-2 hover:text-health-600 transition-colors ${
                location.pathname === link.path ? "text-health-600" : ""
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              {link.name}
            </Link>
          ))}
          {isAuthenticated && (
            <>
              <Link
                to="/appointments"
                className={`py-2 hover:text-health-600 transition-colors ${
                  location.pathname === "/appointments" ? "text-health-600" : ""
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Appointments
              </Link>
              <Link
                to="/results"
                className={`py-2 hover:text-health-600 transition-colors ${
                  location.pathname === "/results" ? "text-health-600" : ""
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Results
              </Link>
              <Link
                to="/dashboard"
                className={`py-2 hover:text-health-600 transition-colors ${
                  location.pathname === "/dashboard" ? "text-health-600" : ""
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Dashboard
              </Link>
            </>
          )}
          <div className="flex flex-col gap-3 pt-4 border-t">
            {!isAuthenticated ? (
              <>
                <Link to="/login" onClick={() => setMobileMenuOpen(false)}>
                  <Button variant="outline" className="w-full">
                    Login
                  </Button>
                </Link>
                <Link to="/register" onClick={() => setMobileMenuOpen(false)}>
                  <Button className="bg-health-600 hover:bg-health-700 text-white w-full">
                    Register
                  </Button>
                </Link>
              </>
            ) : (
              <Button 
                onClick={() => {
                  handleLogout();
                  setMobileMenuOpen(false);
                }}
                variant="outline" 
                className="w-full flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
export { Navbar };
