{"name": "centpercent-care-backend", "version": "1.0.0", "description": "Backend API for CentPercent Care Connect", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:db": "ts-node src/test-db-connection.ts", "migrate": "ts-node src/database/migrate.ts", "migrate:reset": "ts-node src/database/reset.ts"}, "keywords": ["healthcare", "api", "postgresql", "express"], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "jest": "^29.7.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}}