
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { ReactElement } from "react";

interface AppointmentType {
  id: string;
  name: string;
  icon: ReactElement;
  category: string;
}

interface AppointmentTypeSelectionProps {
  appointmentTypes: AppointmentType[];
  categories: string[];
  selectedCategory: string;
  appointmentType: string;
  onCategoryChange: (category: string) => void;
  onTypeChange: (typeId: string) => void;
}

const AppointmentTypeSelection = ({
  appointmentTypes,
  categories,
  selectedCategory,
  appointmentType,
  onCategoryChange,
  onTypeChange
}: AppointmentTypeSelectionProps) => {
  const filteredAppointmentTypes = selectedCategory === "All" 
    ? appointmentTypes 
    : appointmentTypes.filter(type => type.category === selectedCategory);

  return (
    <div className="space-y-6 animate-fade-in">
      <h2 className="text-xl font-semibold text-slate-900 mb-4">
        Select a Service
      </h2>

      <div className="mb-6">
        <Label htmlFor="category" className="mb-2">Category</Label>
        <Select
          value={selectedCategory}
          onValueChange={onCategoryChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[400px] overflow-y-auto p-1">
        {filteredAppointmentTypes.map((type) => (
          <button
            key={type.id}
            type="button"
            className={`p-4 rounded-lg border-2 transition-all ${
              appointmentType === type.id
                ? "border-health-600 bg-health-50"
                : "border-slate-200 hover:border-health-200"
            }`}
            onClick={() => onTypeChange(type.id)}
          >
            <div className="flex items-center text-left">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                  appointmentType === type.id
                    ? "bg-health-600 text-white"
                    : "bg-slate-100 text-slate-500"
                }`}
              >
                {type.icon}
              </div>
              <div>
                <span className="font-medium text-slate-900">
                  {type.name}
                </span>
                <p className="text-xs text-slate-500">
                  {type.category}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default AppointmentTypeSelection;
