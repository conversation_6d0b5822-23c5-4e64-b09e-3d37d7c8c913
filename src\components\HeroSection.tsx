
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const HeroSection = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section className="relative h-screen min-h-[600px] flex items-center overflow-hidden bg-gradient-to-b from-health-50/60 to-white pt-16">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-[10%] left-[5%] w-64 h-64 bg-health-200 rounded-full opacity-20 blur-3xl animate-float"></div>
        <div className="absolute bottom-[20%] right-[10%] w-80 h-80 bg-health-300 rounded-full opacity-20 blur-3xl animate-float" style={{ animationDelay: "2s" }}></div>
      </div>

      <div className="container mx-auto px-4 z-10 flex flex-col lg:flex-row items-center gap-12">
        <div className={`lg:w-1/2 space-y-6 transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="inline-block bg-health-100 text-health-800 px-4 py-1 rounded-full text-sm font-medium mb-2 animate-fade-in-down" style={{ animationDelay: "0.3s" }}>
            Advanced Medical Imaging
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight text-balance">
            Your Health, <span className="text-health-600">Our Priority</span>
          </h1>
          <p className="text-lg md:text-xl text-slate-700 max-w-xl leading-relaxed text-balance">
            Cent'Percent HealthCare provides state-of-the-art imaging services including Ultrasound, MRI, and X-Ray with a focus on patient comfort and accurate diagnostics.
          </p>
          <div className="flex flex-wrap gap-4 pt-2">
            <Button
              asChild
              size="lg" 
              className="bg-health-600 hover:bg-health-700 text-white rounded-full text-lg px-8 neo-button"
            >
              <Link to="/appointments">
                Book Appointment
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-health-300 text-health-700 hover:bg-health-50 rounded-full text-lg px-8 hover-lift"
            >
              <Link to="/services">
                Our Services
              </Link>
            </Button>
          </div>
          <div className="pt-4">
            <div className="flex items-center gap-4">
              <div className="flex -space-x-2">
                <div className="w-10 h-10 rounded-full bg-health-300 flex items-center justify-center text-health-800 text-xs font-medium">99%</div>
                <div className="w-10 h-10 rounded-full bg-health-400 flex items-center justify-center text-white text-xs font-medium">98%</div>
                <div className="w-10 h-10 rounded-full bg-health-600 flex items-center justify-center text-white text-xs font-medium">100%</div>
              </div>
              <p className="text-sm text-slate-600">
                <span className="font-medium">Trusted by 10,000+ patients</span> with 99% satisfaction rate
              </p>
            </div>
          </div>
        </div>
        
        <div className={`lg:w-1/2 transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-20'}`}>
          <div className="relative">
            <div className="absolute -inset-4 bg-health-100 rounded-2xl transform rotate-3 opacity-70"></div>
            <div className="absolute -inset-4 bg-health-200 rounded-2xl transform -rotate-3 opacity-50"></div>
            <div className="bg-white p-2 rounded-2xl shadow-xl relative overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&q=80&w=800&h=600"
                alt="Advanced medical imaging technology"
                className="w-full h-auto rounded-xl object-cover aspect-[4/3]"
                loading="lazy"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 glass-card p-4 rounded-lg shadow-lg animate-fade-in-right" style={{ animationDelay: "0.6s" }}>
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-full">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <p className="text-slate-900 font-medium">Certified Technology</p>
                  <p className="text-xs text-slate-600">FDA & CE approved equipment</p>
                </div>
              </div>
            </div>
            <div className="absolute -top-6 -left-6 glass-card p-4 rounded-lg shadow-lg animate-fade-in-left" style={{ animationDelay: "0.9s" }}>
              <div className="flex items-center gap-3">
                <div className="bg-health-100 p-2 rounded-full">
                  <svg className="w-5 h-5 text-health-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <div>
                  <p className="text-slate-900 font-medium">Expert Staff</p>
                  <p className="text-xs text-slate-600">Board-certified radiologists</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
