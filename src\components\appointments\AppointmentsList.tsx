
import { useState } from "react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Trash2, CheckCircle } from "lucide-react";
import { AlertDialog } from "../ui/alert-dialog";
import { getAppointmentTypeIcon } from "./appointmentUtils";
import { ReactElement } from "react";

export interface Appointment {
  id: string;
  type: string;
  date: string;
  time: string;
  doctor: string;
  location: string;
  canceled?: boolean;
  confirmation_number?: string;
  notes?: string;
  status: string;
}

interface AppointmentsListProps {
  appointments: Appointment[];
  onReschedule: (appointment: Appointment) => void;
  onCancelDialogOpen: (appointment: Appointment) => void;
}

const AppointmentsList = ({ 
  appointments, 
  onReschedule, 
  onCancelDialogOpen 
}: AppointmentsListProps) => {
  return (
    <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
      <Table>
        <TableCaption>
          Your appointments
        </TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Service</TableHead>
            <TableHead>Date & Time</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {appointments.map((appointment) => {
            const isCompleted = appointment.status === 'completed';
            const isCancelled = appointment.status === 'cancelled';
            
            return (
              <TableRow key={appointment.id} className={isCompleted ? "bg-slate-50" : ""}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                      isCompleted ? "bg-green-100 text-green-600" : 
                      isCancelled ? "bg-red-100 text-red-600" : 
                      "bg-health-100 text-health-600"
                    }`}>
                      {isCompleted ? <CheckCircle className="h-4 w-4" /> : getAppointmentTypeIcon(appointment.type)}
                    </div>
                    <div>
                      <p className={isCompleted ? "text-slate-600" : ""}>{appointment.type}</p>
                      <p className="text-xs text-slate-500">With {appointment.doctor}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <p className={isCompleted ? "text-slate-600" : ""}>{format(new Date(appointment.date), "MMM d, yyyy")}</p>
                    <p className="text-xs text-slate-500">{appointment.time}</p>
                  </div>
                </TableCell>
                <TableCell className={isCompleted ? "text-slate-600" : ""}>{appointment.location}</TableCell>
                <TableCell>
                  <span className={`inline-block text-xs px-2 py-1 rounded-full ${
                    appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                    appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {!isCompleted && !isCancelled && (
                      <>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onReschedule(appointment)}
                        >
                          Reschedule
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onCancelDialogOpen(appointment)}
                          className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-1" /> Cancel
                        </Button>
                      </>
                    )}
                    {isCompleted && (
                      <span className="text-xs text-slate-500 italic">Completed</span>
                    )}
                    {isCancelled && (
                      <span className="text-xs text-slate-500 italic">Cancelled</span>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default AppointmentsList;
