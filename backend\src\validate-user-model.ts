// Simple validation script to check User model interfaces
import { User, CreateUserData, UpdateUserData, LoginCredentials, JWTPayload } from './models/User';

console.log('🧪 Validating User Model interfaces...');

// Test User interface
const sampleUser: User = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  phoneNumber: '+1234567890',
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Test CreateUserData interface
const createData: CreateUserData = {
  email: '<EMAIL>',
  password: 'securepassword123',
  firstName: 'Jane',
  lastName: 'Smith',
  phoneNumber: '+0987654321',
};

// Test UpdateUserData interface
const updateData: UpdateUserData = {
  firstName: 'Updated',
  lastName: 'Name',
};

// Test LoginCredentials interface
const loginCreds: LoginCredentials = {
  email: '<EMAIL>',
  password: 'password123',
};

// Test JWTPayload interface
const jwtPayload: JWTPayload = {
  userId: '123e4567-e89b-12d3-a456-426614174000',
  email: '<EMAIL>',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
};

console.log('✅ User interface validation passed');
console.log('✅ CreateUserData interface validation passed');
console.log('✅ UpdateUserData interface validation passed');
console.log('✅ LoginCredentials interface validation passed');
console.log('✅ JWTPayload interface validation passed');

console.log('\n🎉 All User Model interfaces are properly defined!');
console.log('\n📋 Interface Summary:');
console.log('- User: Public user data (no sensitive info)');
console.log('- CreateUserData: Data needed for user registration');
console.log('- UpdateUserData: Data for profile updates');
console.log('- LoginCredentials: Email and password for login');
console.log('- JWTPayload: Data stored in JWT tokens');

export { sampleUser, createData, updateData, loginCreds, jwtPayload };
