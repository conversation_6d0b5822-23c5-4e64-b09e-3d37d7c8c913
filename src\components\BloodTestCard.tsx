
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { BloodTestIcon, UrineTestIcon, PathologyIcon } from "@/components/CustomIcons";

interface BloodTestCardProps {
  title: string;
  description: string;
  icon: "blood" | "urine" | "pathology";
  features: string[];
  price: string;
  turnaround: string;
  preparation: string;
  highlighted?: boolean;
}

const BloodTestCard = ({
  title,
  description,
  icon,
  features,
  price,
  turnaround,
  preparation,
  highlighted = false,
}: BloodTestCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  const getIcon = () => {
    switch (icon) {
      case "blood":
        return <BloodTestIcon size={32} />;
      case "urine":
        return <UrineTestIcon size={32} />;
      case "pathology":
        return <PathologyIcon size={32} />;
      default:
        return <BloodTestIcon size={32} />;
    }
  };

  return (
    <div
      className={`relative rounded-xl overflow-hidden transition-all duration-300 
        ${
          highlighted
            ? "bg-gradient-to-br from-health-50 to-health-100 border-2 border-health-200 shadow-lg"
            : "bg-white border border-slate-200 hover:border-health-200 hover:shadow-md"
        }
        ${isHovered ? "transform scale-[1.02]" : ""}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {highlighted && (
        <div className="absolute top-0 right-0">
          <div className="bg-health-600 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
            Popular
          </div>
        </div>
      )}

      <div className="p-6">
        <div
          className={`w-16 h-16 rounded-full flex items-center justify-center mb-6 transition-all duration-300 
            ${
              highlighted
                ? "bg-health-600 text-white"
                : "bg-health-100 text-health-600"
            }
            ${isHovered && !highlighted ? "bg-health-200" : ""}
          `}
        >
          {getIcon()}
        </div>

        <h3 className="text-xl font-bold text-slate-900 mb-2">{title}</h3>
        <p className="text-slate-600 mb-6">{description}</p>

        <div className="mb-6">
          <div className="text-sm font-medium text-slate-500 mb-1">Starting from</div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-slate-900">{price}</span>
            <span className="text-slate-600 ml-2">/ test</span>
          </div>
        </div>

        <div className="space-y-2 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-health-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-slate-700">Results in {turnaround}</span>
          </div>
          <div className="flex items-center">
            <svg className="w-5 h-5 text-health-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-slate-700">{preparation}</span>
          </div>
        </div>

        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <svg
                className={`w-5 h-5 ${
                  highlighted ? "text-health-600" : "text-health-500"
                } mr-3 mt-0.5`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span className="text-slate-600">{feature}</span>
            </li>
          ))}
        </ul>

        <div className="space-y-3">
          <Button
            asChild
            className={`w-full ${
              highlighted
                ? "bg-health-600 hover:bg-health-700"
                : "bg-health-500 hover:bg-health-600"
            } text-white`}
          >
            <Link to="/appointments">Book Test</Link>
          </Button>
          <Button
            asChild
            variant="outline"
            className="w-full border-health-200 text-health-700 hover:bg-health-50"
          >
            <Link to="/services">Learn More</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BloodTestCard;
