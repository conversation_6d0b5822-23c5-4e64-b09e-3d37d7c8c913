import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ServiceCard from "@/components/ServiceCard";
import BloodTestCard from "@/components/BloodTestCard";
import { BloodTestIcon, UrineTestIcon, PathologyIcon } from "@/components/CustomIcons";

const Services = () => {
  const [activeTab, setActiveTab] = useState("imaging");

  const imagingServices = [
    {
      title: "Ultrasound",
      description: "Non-invasive imaging using sound waves to visualize internal organs and structures.",
      icon: "ultrasound" as const,
      features: [
        "Abdominal ultrasound",
        "Obstetric ultrasound",
        "Vascular ultrasound",
        "Thyroid ultrasound",
        "Quick and painless procedure"
      ],
      price: "$150",
      learnMoreLink: "/services#ultrasound",
      appointmentLink: "/appointments",
      highlighted: false,
    },
    {
      title: "ECG",
      description: "Electrocardiogram testing to evaluate heart rhythm and detect cardiac conditions.",
      icon: "ecg" as const,
      features: [
        "Heart rhythm analysis",
        "Cardiac condition detection",
        "Arrhythmia screening",
        "Heart rate monitoring",
        "Quick and non-invasive"
      ],
      price: "$120",
      learnMoreLink: "/services#ecg",
      appointmentLink: "/appointments",
      highlighted: true,
    },
    {
      title: "X-Ray",
      description: "Quick and effective diagnostic imaging for bones, chest, and other structures.",
      icon: "xray" as const,
      features: [
        "Chest X-ray",
        "Bone X-ray",
        "Dental X-ray",
        "Joint X-ray",
        "Fast results available"
      ],
      price: "$95",
      learnMoreLink: "/services#xray",
      appointmentLink: "/appointments",
      highlighted: false,
    },
  ];

  const bloodTests = [
    {
      title: "Comprehensive Metabolic Panel",
      description: "Evaluates kidney and liver function, electrolyte and fluid balance, and protein levels.",
      icon: "blood" as const,
      features: [
        "Glucose levels",
        "Kidney function",
        "Liver function",
        "Electrolyte balance",
        "Protein levels"
      ],
      price: "$80",
      turnaround: "24-48 hours",
      preparation: "8-12 hour fasting required",
      highlighted: false,
    },
    {
      title: "Lipid Profile",
      description: "Measures cholesterol levels to assess risk of cardiovascular disease.",
      icon: "blood" as const,
      features: [
        "Total cholesterol",
        "HDL (good) cholesterol",
        "LDL (bad) cholesterol",
        "Triglycerides",
        "Cholesterol ratio analysis"
      ],
      price: "$65",
      turnaround: "24 hours",
      preparation: "9-12 hour fasting required",
      highlighted: true,
    },
    {
      title: "Complete Blood Count (CBC)",
      description: "Evaluates overall health and detects a wide range of disorders including anemia and infection.",
      icon: "blood" as const,
      features: [
        "Red blood cell count",
        "White blood cell count",
        "Platelet count",
        "Hemoglobin levels",
        "Hematocrit percentage"
      ],
      price: "$45",
      turnaround: "Same day",
      preparation: "No special preparation",
      highlighted: false,
    },
    {
      title: "Glucose Testing",
      description: "Measures blood sugar levels to diagnose and monitor diabetes and prediabetes.",
      icon: "blood" as const,
      features: [
        "Fasting glucose test",
        "Post-prandial glucose test",
        "Glucose tolerance test",
        "HbA1c (glycated hemoglobin)",
        "Insulin resistance assessment"
      ],
      price: "$55",
      turnaround: "24 hours",
      preparation: "Varies by test type",
      highlighted: false,
    },
    {
      title: "Thyroid Function Panel",
      description: "Comprehensive assessment of thyroid gland function and hormones.",
      icon: "blood" as const,
      features: [
        "TSH (Thyroid Stimulating Hormone)",
        "Free T3",
        "Free T4",
        "Thyroid antibodies",
        "Reverse T3 (optional)"
      ],
      price: "$95",
      turnaround: "24-48 hours",
      preparation: "No special preparation",
      highlighted: false,
    },
    {
      title: "Comprehensive Blood Chemistry",
      description: "Complete analysis of blood composition, cellular components, and chemistry.",
      icon: "blood" as const,
      features: [
        "Comprehensive CBC",
        "Full metabolic panel",
        "Lipid profile",
        "Electrolyte measurement",
        "Liver and kidney function tests"
      ],
      price: "$160",
      turnaround: "48 hours",
      preparation: "12 hour fasting required",
      highlighted: false,
    },
    {
      title: "Iron Studies",
      description: "Evaluates iron status in the body to diagnose conditions like anemia or iron overload.",
      icon: "blood" as const,
      features: [
        "Serum iron",
        "Ferritin levels",
        "Total iron binding capacity",
        "Transferrin saturation",
        "Hemoglobin analysis"
      ],
      price: "$75",
      turnaround: "24-48 hours",
      preparation: "Morning sample preferred",
      highlighted: false,
    },
    {
      title: "Vitamin D Test",
      description: "Measures vitamin D levels to identify deficiency or toxicity.",
      icon: "blood" as const,
      features: [
        "25-hydroxy vitamin D test",
        "Bone health assessment",
        "Immune function correlation",
        "Seasonal variation analysis",
        "Supplementation guidance"
      ],
      price: "$70",
      turnaround: "48 hours",
      preparation: "No special preparation",
      highlighted: false,
    },
  ];

  const urineTests = [
    {
      title: "Urinalysis",
      description: "Examines the physical, chemical, and microscopic aspects of urine to detect disorders.",
      icon: "urine" as const,
      features: [
        "Kidney function assessment",
        "Urinary tract infection screening",
        "Diabetes screening",
        "Liver function assessment",
        "Hydration status"
      ],
      price: "$40",
      turnaround: "Same day",
      preparation: "Mid-stream clean catch sample",
      highlighted: false,
    },
    {
      title: "24-Hour Urine Collection",
      description: "Comprehensive evaluation of kidney function and metabolic disorders over 24 hours.",
      icon: "urine" as const,
      features: [
        "Protein excretion",
        "Creatinine clearance",
        "Calcium excretion",
        "Sodium and potassium levels",
        "Uric acid levels"
      ],
      price: "$120",
      turnaround: "2-3 days",
      preparation: "Special collection container provided",
      highlighted: true,
    },
    {
      title: "Drug Screening",
      description: "Detects presence of prescription and illegal drugs in urine samples.",
      icon: "urine" as const,
      features: [
        "5-panel drug test",
        "10-panel drug test available",
        "Prescription medication monitoring",
        "Alcohol metabolite detection",
        "Legal documentation available"
      ],
      price: "$85",
      turnaround: "24-48 hours",
      preparation: "Avoid certain foods and medications",
      highlighted: false,
    },
  ];

  const specialTests = [
    {
      title: "Thyroid Function Panel",
      description: "Comprehensive assessment of thyroid health and function.",
      icon: "pathology" as const,
      features: [
        "TSH levels",
        "Free T4 levels",
        "Free T3 levels",
        "Thyroid antibodies",
        "Comprehensive analysis"
      ],
      price: "$110",
      turnaround: "24-48 hours",
      preparation: "Morning sample preferred",
      highlighted: false,
    },
    {
      title: "Vitamin Panel",
      description: "Evaluates essential vitamin levels to identify deficiencies that affect health.",
      icon: "pathology" as const,
      features: [
        "Vitamin D",
        "Vitamin B12",
        "Folate levels",
        "Vitamin A & E",
        "Complete nutritional assessment"
      ],
      price: "$155",
      turnaround: "3-4 days",
      preparation: "8 hour fasting required",
      highlighted: true,
    },
    {
      title: "Hormone Panel",
      description: "Measures hormone levels to evaluate endocrine function and reproductive health.",
      icon: "pathology" as const,
      features: [
        "Estrogen & progesterone",
        "Testosterone",
        "Cortisol levels",
        "DHEA levels",
        "Comprehensive hormonal assessment"
      ],
      price: "$195",
      turnaround: "3-5 days",
      preparation: "Morning sample, specific timing for women",
      highlighted: false,
    },
  ];

  return (
    <>
      <Navbar />
      
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-health-50 to-health-100 py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
                Comprehensive Diagnostic Services
              </h1>
              <p className="text-lg md:text-xl text-slate-700 mb-8">
                From advanced imaging to detailed laboratory tests, we provide the full spectrum of diagnostic services to support your healthcare journey.
              </p>
            </div>
          </div>
        </section>

        {/* Services Tabs Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <Tabs defaultValue="imaging" className="w-full" onValueChange={setActiveTab}>
              <div className="flex justify-center mb-12 overflow-x-auto">
                <TabsList className="bg-slate-100 p-1 rounded-full">
                  <TabsTrigger 
                    value="imaging" 
                    className={`px-6 py-3 rounded-full transition-all ${
                      activeTab === "imaging" ? "bg-health-600 text-white" : "text-slate-700"
                    }`}
                  >
                    Imaging Services
                  </TabsTrigger>
                  <TabsTrigger 
                    value="blood" 
                    className={`px-6 py-3 rounded-full transition-all ${
                      activeTab === "blood" ? "bg-health-600 text-white" : "text-slate-700"
                    }`}
                  >
                    Blood Tests
                  </TabsTrigger>
                  <TabsTrigger 
                    value="urine" 
                    className={`px-6 py-3 rounded-full transition-all ${
                      activeTab === "urine" ? "bg-health-600 text-white" : "text-slate-700"
                    }`}
                  >
                    Urine Tests
                  </TabsTrigger>
                  <TabsTrigger 
                    value="special" 
                    className={`px-6 py-3 rounded-full transition-all ${
                      activeTab === "special" ? "bg-health-600 text-white" : "text-slate-700"
                    }`}
                  >
                    Specialty Tests
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="imaging">
                <div className="text-center mb-12">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Advanced Imaging Services
                  </h2>
                  <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                    State-of-the-art diagnostic imaging services with a focus on patient comfort and accurate results.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {imagingServices.map((service, index) => (
                    <ServiceCard
                      key={index}
                      {...service}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="blood">
                <div className="text-center mb-12">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Laboratory Blood Testing Services
                  </h2>
                  <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                    Comprehensive blood tests to assess your overall health, detect diseases, and monitor existing conditions with accurate results and quick turnaround times.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {bloodTests.map((test, index) => (
                    <BloodTestCard
                      key={index}
                      {...test}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="urine">
                <div className="text-center mb-12">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Urine Testing Services
                  </h2>
                  <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                    Detailed urinalysis to evaluate kidney function, detect infections, and screen for various metabolic conditions.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {urineTests.map((test, index) => (
                    <BloodTestCard
                      key={index}
                      {...test}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="special">
                <div className="text-center mb-12">
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                    Specialty Laboratory Tests
                  </h2>
                  <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                    Specialized diagnostic tests for specific health concerns, nutritional status, and hormonal balances.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {specialTests.map((test, index) => (
                    <BloodTestCard
                      key={index}
                      {...test}
                    />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Insurance & Payment Section */}
        <section className="py-16 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
                Insurance & Payment Options
              </h2>
              <p className="text-lg text-slate-600">
                We accept most major insurance plans and offer flexible payment options to make healthcare accessible.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-slate-100">
                <div className="w-12 h-12 bg-health-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-health-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Insurance Coverage</h3>
                <p className="text-slate-600">We work with most major insurance providers to ensure your diagnostic tests are covered.</p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-slate-100">
                <div className="w-12 h-12 bg-health-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-health-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Self-Pay Options</h3>
                <p className="text-slate-600">Competitive pricing and transparent costs for patients without insurance coverage.</p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-slate-100">
                <div className="w-12 h-12 bg-health-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-health-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Billing Assistance</h3>
                <p className="text-slate-600">Our dedicated team is available to help you understand costs and navigate billing questions.</p>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
};

export default Services;
