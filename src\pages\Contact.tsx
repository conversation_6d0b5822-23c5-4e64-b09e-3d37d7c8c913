
import React from 'react';
import { Mail, Phone, MapPin } from 'lucide-react';
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const Contact = () => {
  return (
    <>
      <Navbar />
      <div className="container mx-auto px-4 py-12 pt-32">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-health-900 mb-8 text-center">Contact Us</h1>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Contact Information Section */}
            <div className="bg-soft-purple p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold text-health-800 mb-4">Get in Touch</h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="text-health-600" size={24} />
                  <div>
                    <p className="font-medium">Support Email</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-health-700 hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="text-health-600" size={24} />
                  <div>
                    <p className="font-medium">Support Phone</p>
                    <a 
                      href="tel:1-800-CENT" 
                      className="text-health-700 hover:underline"
                    >
                      1-800-CENT
                    </a>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <MapPin className="text-health-600" size={24} />
                  <div>
                    <p className="font-medium">Address</p>
                    <p className="text-slate-700">123 Healthcare Lane, Medical City, HC 12345</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form Section */}
            <div>
              <h2 className="text-xl font-semibold text-health-800 mb-4">Send us a Message</h2>
              <form className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-2">Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-health-500"
                    placeholder="Your Name"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">Email</label>
                  <input 
                    type="email" 
                    id="email" 
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-health-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-2">Message</label>
                  <textarea 
                    id="message" 
                    rows={4} 
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-health-500"
                    placeholder="How can we help you?"
                  ></textarea>
                </div>
                <button 
                  type="submit" 
                  className="w-full bg-health-600 text-white py-2 rounded-md hover:bg-health-700 transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default Contact;
