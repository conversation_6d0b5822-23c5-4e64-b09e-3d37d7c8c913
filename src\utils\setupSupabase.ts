
import { supabase } from '@/integrations/supabase/client';
import { checkTableExists, getSetupInstructions } from './databaseUtils';

/**
 * Initializes the database by checking for required tables
 * and creating them if they don't exist.
 */
export const initializeDatabase = async () => {
  console.log("Checking database setup...");
  
  // Check for required tables
  const usersTableExists = await checkTableExists("users");
  const appointmentsTableExists = await checkTableExists("appointments");
  const resultsTableExists = await checkTableExists("results");
  
  // Log tables status
  console.log("Database tables status:", {
    users: usersTableExists ? "exists" : "missing",
    appointments: appointmentsTableExists ? "exists" : "missing",
    results: resultsTableExists ? "exists" : "missing"
  });
  
  // If tables don't exist, it means the user hasn't set up the database yet
  if (!usersTableExists || !appointmentsTableExists || !resultsTableExists) {
    console.warn("Some required database tables do not exist. Please refer to the setup instructions.");
    console.info(getSetupInstructions());
    
    // We won't attempt to create tables here as it requires proper permissions
    // The user should follow the setup instructions instead
  }
  
  return {
    usersTableExists,
    appointmentsTableExists,
    resultsTableExists
  };
};

