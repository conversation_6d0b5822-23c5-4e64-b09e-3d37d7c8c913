
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileUp, Search, Check, AlertCircle } from "lucide-react";

const Admin = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [users, setUsers] = useState<any[]>([]);
  const [appointments, setAppointments] = useState<any[]>([]);
  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form states for result upload
  const [resultTitle, setResultTitle] = useState("");
  const [resultType, setResultType] = useState("");
  const [resultStatus, setResultStatus] = useState("Ready");
  const [resultFindings, setResultFindings] = useState("");
  const [resultRecommendation, setResultRecommendation] = useState("");
  const [resultDialogOpen, setResultDialogOpen] = useState(false);

  useEffect(() => {
    // Check if user is authenticated and is admin (for demo, we'll check via email)
    if (!isAuthenticated) {
      navigate("/login");
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch users
        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select("*");

        if (usersError) throw usersError;
        setUsers(usersData || []);

        // Fetch appointments
        const { data: appointmentsData, error: appointmentsError } = await supabase
          .from("appointments")
          .select("*, users(email, first_name, last_name)");

        if (appointmentsError) throw appointmentsError;
        setAppointments(appointmentsData || []);

        // Fetch results
        const { data: resultsData, error: resultsError } = await supabase
          .from("results")
          .select("*, users(email, first_name, last_name)");

        if (resultsError) throw resultsError;
        setResults(resultsData || []);
      } catch (error: any) {
        console.error("Error fetching admin data:", error.message);
        toast({
          title: "Error loading data",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated, navigate, toast]);

  const handleUploadResult = (appointment: any) => {
    // Pre-fill some data based on the appointment
    setSelectedAppointment(appointment);
    setResultTitle(`${appointment.type} Results`);
    setResultType(appointment.type);
    setResultDialogOpen(true);
  };

  const submitResultUpload = async () => {
    if (!selectedAppointment || !resultTitle || !resultType) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Create a new test result entry
      const { data, error } = await supabase
        .from("results")
        .insert([
          {
            user_id: selectedAppointment.user_id,
            title: resultTitle,
            type: resultType,
            date: new Date().toISOString().split('T')[0],
            status: resultStatus,
            doctor: selectedAppointment.doctor,
            findings: resultFindings,
            recommendation: resultRecommendation,
            department: "Radiology",
            details: { appointment_id: selectedAppointment.id, created_by: user?.id }
          }
        ])
        .select();

      if (error) throw error;

      // Success
      toast({
        title: "Result uploaded successfully",
        description: `Test results uploaded for ${selectedAppointment.users?.first_name} ${selectedAppointment.users?.last_name}`,
      });

      // Reset form and close dialog
      resetForm();
      setResultDialogOpen(false);

      // Refresh results list
      const { data: refreshedData, error: refreshError } = await supabase
        .from("results")
        .select("*, users(email, first_name, last_name)");

      if (refreshError) throw refreshError;
      setResults(refreshedData || []);
    } catch (error: any) {
      console.error("Error uploading test result:", error);
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSelectedAppointment(null);
    setResultTitle("");
    setResultType("");
    setResultStatus("Ready");
    setResultFindings("");
    setResultRecommendation("");
  };

  // Filter appointments based on search term
  const filteredAppointments = appointments.filter((appointment) => {
    const patientName = `${appointment.users?.first_name} ${appointment.users?.last_name}`.toLowerCase();
    const appointmentType = appointment.type.toLowerCase();
    const searchLower = searchTerm.toLowerCase();
    
    return patientName.includes(searchLower) || appointmentType.includes(searchLower);
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-12 w-12 border-4 border-health-600 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 p-6">
      <div className="max-w-7xl mx-auto">
        <header className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Admin Dashboard</h1>
              <p className="text-slate-600">View and manage health records</p>
            </div>
            <Button onClick={() => navigate("/dashboard")} variant="outline">
              Back to Dashboard
            </Button>
          </div>
        </header>

        <Tabs defaultValue="users" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="results">Test Results</TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="bg-white p-6 rounded-lg shadow-sm">
            <Table>
              <TableCaption>List of registered users</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Created At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length > 0 ? (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.id.substring(0, 8)}...</TableCell>
                      <TableCell>{`${user.first_name} ${user.last_name}`}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.phone_number}</TableCell>
                      <TableCell>{new Date(user.created_at).toLocaleString()}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-slate-500">
                      No users found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="appointments" className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Manage Appointments</h2>
              <div className="relative w-64">
                <Input
                  placeholder="Search appointments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
              </div>
            </div>
            
            <Table>
              <TableCaption>List of appointments</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Patient</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Doctor</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAppointments.length > 0 ? (
                  filteredAppointments.map((appointment) => (
                    <TableRow key={appointment.id}>
                      <TableCell>
                        {appointment.users?.first_name} {appointment.users?.last_name}
                      </TableCell>
                      <TableCell>{appointment.type}</TableCell>
                      <TableCell>{`${appointment.date} at ${appointment.time}`}</TableCell>
                      <TableCell>{appointment.doctor}</TableCell>
                      <TableCell>{appointment.location}</TableCell>
                      <TableCell>
                        <Button 
                          onClick={() => handleUploadResult(appointment)} 
                          size="sm" 
                          className="bg-health-600 hover:bg-health-700"
                        >
                          <FileUp size={16} className="mr-1" />
                          Upload Results
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-slate-500">
                      No appointments found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="results" className="bg-white p-6 rounded-lg shadow-sm">
            <Table>
              <TableCaption>List of test results</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Patient</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Doctor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.length > 0 ? (
                  results.map((result) => (
                    <TableRow key={result.id}>
                      <TableCell className="font-medium">{result.id.substring(0, 8)}...</TableCell>
                      <TableCell>
                        {result.users?.first_name} {result.users?.last_name}
                      </TableCell>
                      <TableCell>{result.type}</TableCell>
                      <TableCell>{result.date}</TableCell>
                      <TableCell>
                        <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                          result.status === 'Ready' ? 'bg-green-100 text-green-800' : 
                          result.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {result.status}
                        </span>
                      </TableCell>
                      <TableCell>{result.doctor}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-slate-500">
                      No results found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>
      </div>

      {/* Dialog for uploading test results */}
      <Dialog open={resultDialogOpen} onOpenChange={setResultDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload Test Results</DialogTitle>
            <DialogDescription>
              Enter the test results for patient: {selectedAppointment && `${selectedAppointment.users?.first_name} ${selectedAppointment.users?.last_name}`}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title
              </Label>
              <Input
                id="title"
                value={resultTitle}
                onChange={(e) => setResultTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type
              </Label>
              <Select value={resultType} onValueChange={setResultType}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select test type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Blood Test">Blood Test</SelectItem>
                  <SelectItem value="X-Ray">X-Ray</SelectItem>
                  <SelectItem value="MRI">MRI</SelectItem>
                  <SelectItem value="Ultrasound">Ultrasound</SelectItem>
                  <SelectItem value="CT Scan">CT Scan</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select value={resultStatus} onValueChange={setResultStatus}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Ready">Ready</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="findings" className="text-right">
                Findings
              </Label>
              <Textarea
                id="findings"
                value={resultFindings}
                onChange={(e) => setResultFindings(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="recommendation" className="text-right">
                Recommendation
              </Label>
              <Textarea
                id="recommendation"
                value={resultRecommendation}
                onChange={(e) => setResultRecommendation(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setResultDialogOpen(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button 
              onClick={submitResultUpload} 
              disabled={isSubmitting} 
              className="bg-health-600 hover:bg-health-700"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Uploading...</span>
                </div>
              ) : (
                <>
                  <Check size={16} className="mr-2" />
                  Save Results
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Admin;
