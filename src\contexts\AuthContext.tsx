
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

// Define our user type
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
}

// Define insurance info type
interface InsuranceInfo {
  provider: string;
  policyNumber: string;
  groupNumber: string;
}

// Define the context shape
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<User> & { password: string, insuranceInfo?: InsuranceInfo | null }) => Promise<void>;
  logout: () => Promise<void>;
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
});

// AuthProvider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Format Supabase user data to our User type
  const formatUser = (userData: any): User => {
    return {
      id: userData.id,
      email: userData.email,
      firstName: userData.first_name || userData.user_metadata?.first_name || '',
      lastName: userData.last_name || userData.user_metadata?.last_name || '',
      phoneNumber: userData.phone_number || userData.user_metadata?.phone_number || '',
    };
  };

  // Check for existing authentication on mount
  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          // Fetch additional user data from the users table
          const fetchUserData = async () => {
            const { data: userData, error: userError } = await supabase
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single();

            if (userError && userError.code !== 'PGRST116') {
              console.error('Error fetching user data on auth change:', userError);
            }

            if (userData) {
              setUser({
                id: session.user.id,
                email: session.user.email || '',
                firstName: userData.first_name,
                lastName: userData.last_name,
                phoneNumber: userData.phone_number,
              });
            } else {
              setUser(formatUser(session.user));
            }
          };

          // Use setTimeout to prevent deadlocks
          setTimeout(fetchUserData, 0);
          setIsLoading(false);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setIsLoading(false);
        }
      }
    );

    // THEN check for existing session
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          throw sessionError;
        }
        
        if (session?.user) {
          // Fetch additional user data from the users table
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();
          
          if (userError && userError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            console.error('Error fetching user data:', userError);
          }
          
          // If user exists in our users table, use that data, otherwise use session data
          if (userData) {
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              firstName: userData.first_name,
              lastName: userData.last_name,
              phoneNumber: userData.phone_number,
            });
          } else {
            // Fallback to session data
            setUser(formatUser(session.user));
          }
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // Auth state change will handle updating the user
    } catch (error: any) {
      console.error("Login failed:", error.message);
      toast({
        title: "Login failed",
        description: error.message || "Invalid credentials",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: Partial<User> & { password: string, insuranceInfo?: InsuranceInfo | null }) => {
    setIsLoading(true);
    try {
      // Register with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email: userData.email || '',
        password: userData.password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            phone_number: userData.phoneNumber,
          },
        },
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Insert into our users table
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: userData.email || '',
            first_name: userData.firstName || '',
            last_name: userData.lastName || '',
            phone_number: userData.phoneNumber || '',
          });

        if (insertError) {
          console.error('Error inserting user data:', insertError);
          toast({
            title: "User data error",
            description: "Account created but user profile data couldn't be saved",
            variant: "destructive",
          });
        }

        // If insurance info is provided, insert it
        if (userData.insuranceInfo) {
          const { error: insuranceError } = await supabase
            .from('insurance_info')
            .insert({
              user_id: data.user.id,
              provider: userData.insuranceInfo.provider,
              policy_number: userData.insuranceInfo.policyNumber,
              group_number: userData.insuranceInfo.groupNumber,
            });

          if (insuranceError) {
            console.error('Error inserting insurance data:', insuranceError);
            toast({
              title: "Insurance data error",
              description: "Account created but insurance data couldn't be saved",
              variant: "destructive",
            });
          }
        }

        // Auth state change will handle updating the user
      }
    } catch (error: any) {
      console.error("Registration failed:", error.message);
      toast({
        title: "Registration failed",
        description: error.message || "An error occurred during registration",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
      setUser(null);
    } catch (error: any) {
      console.error("Logout failed:", error.message);
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
