
/**
 * Security utilities for protecting against common threats
 */

// Basic input sanitization
export const sanitizeInput = (input: string): string => {
  if (!input) return '';
  
  // Replace potentially dangerous characters
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Sanitize and validate URL
export const sanitizeUrl = (url: string): string | null => {
  // Basic URL validation
  const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
  
  if (!urlRegex.test(url)) {
    return null;
  }
  
  // Ensure URL is http or https
  if (!/^https?:\/\//i.test(url)) {
    url = 'https://' + url;
  }
  
  return url;
};

// Token for CSRF protection
export const generateCSRFToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

// Store CSRF token in localStorage
export const setCSRFToken = (): string => {
  const token = generateCSRFToken();
  localStorage.setItem('csrf_token', token);
  return token;
};

// Verify CSRF token
export const verifyCSRFToken = (token: string): boolean => {
  return token === localStorage.getItem('csrf_token');
};
