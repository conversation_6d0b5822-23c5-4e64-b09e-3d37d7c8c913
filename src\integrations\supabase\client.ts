
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://uioefyrmmmzlkcucpzzb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVpb2VmeXJtbW16bGtjdWNwenpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyNzcwMTgsImV4cCI6MjA1ODg1MzAxOH0.uylGNHoganZBbCT6u21LEHEVcjm63ziPFEGQ3CPjra0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
