
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          phone_number: string
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          first_name: string
          last_name: string
          phone_number: string
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string
          last_name?: string
          phone_number?: string
          created_at?: string
        }
      }
      appointments: {
        Row: {
          id: string
          user_id: string
          type: string
          date: string
          time: string
          doctor: string
          location: string
          created_at: string
          canceled: boolean
          confirmation_number?: string
          notes?: string
        }
        Insert: {
          id?: string
          user_id: string
          type: string
          date: string
          time: string
          doctor: string
          location: string
          created_at?: string
          canceled?: boolean
          confirmation_number?: string
          notes?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: string
          date?: string
          time?: string
          doctor?: string
          location?: string
          created_at?: string
          canceled?: boolean
          confirmation_number?: string
          notes?: string
        }
      }
      results: {
        Row: {
          id: string
          user_id: string
          type: string
          date: string
          status: string
          doctor: string
          details: <PERSON><PERSON>
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: string
          date: string
          status: string
          doctor: string
          details?: <PERSON><PERSON>
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: string
          date?: string
          status?: string
          doctor?: string
          details?: Json
          created_at?: string
        }
      }
      insurance_info: {
        Row: {
          id: string
          user_id: string
          provider: string
          policy_number: string
          group_number: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          provider: string
          policy_number: string
          group_number: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          provider?: string
          policy_number?: string
          group_number?: string
          created_at?: string
        }
      }
    }
  }
}
