
import { format } from "date-fns";
import { CalendarIcon, ClockIcon, MailIcon } from "lucide-react";

interface AppointmentConfirmationProps {
  appointmentTypeName: string;
  appointmentTypeIcon: JSX.Element | null;
  locationName: string;
  locationAddress: string;
  date: string;
  time: string;
  email: string;
  notes: string;
}

const AppointmentConfirmation = ({
  appointmentTypeName,
  appointmentTypeIcon,
  locationName,
  locationAddress,
  date,
  time,
  email,
  notes
}: AppointmentConfirmationProps) => {
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <h2 className="text-xl font-semibold text-slate-900 mb-4">
        Confirm Your Appointment
      </h2>
      <div className="bg-slate-50 rounded-lg p-6 border border-slate-200">
        <div className="flex items-start mb-4">
          <div className="w-10 h-10 rounded-full bg-health-100 text-health-600 flex items-center justify-center mr-4">
            {appointmentTypeIcon}
          </div>
          <div>
            <h3 className="font-medium text-slate-900">
              {appointmentTypeName} Appointment
            </h3>
            <p className="text-sm text-slate-600">
              {locationName} - {locationAddress}
            </p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row sm:gap-8 mb-2">
          <div className="flex items-center mb-2">
            <CalendarIcon size={16} className="text-slate-400 mr-2" />
            <span className="text-slate-600">{date ? formatDate(date) : ""}</span>
          </div>
          <div className="flex items-center">
            <ClockIcon size={16} className="text-slate-400 mr-2" />
            <span className="text-slate-600">{time}</span>
          </div>
        </div>
        <div className="flex items-center mb-2">
          <MailIcon size={16} className="text-slate-400 mr-2" />
          <span className="text-slate-600">{email}</span>
        </div>
        {notes && (
          <div className="mt-4 pt-4 border-t border-slate-200">
            <h4 className="text-sm font-medium text-slate-700 mb-1">Additional Notes:</h4>
            <p className="text-sm text-slate-600">{notes}</p>
          </div>
        )}
      </div>
      <div className="bg-blue-50 border border-blue-100 rounded-md p-4 text-sm text-blue-800">
        <p>Please arrive 15 minutes before your scheduled appointment time. Remember to bring your ID and insurance card.</p>
      </div>
    </div>
  );
};

export default AppointmentConfirmation;
