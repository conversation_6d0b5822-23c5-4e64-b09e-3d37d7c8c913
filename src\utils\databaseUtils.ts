
import { supabase } from '@/lib/supabase';

/**
 * Checks if a table exists in the Supabase database
 * @param tableName The name of the table to check
 * @returns Promise<boolean> indicating if the table exists
 */
export const checkTableExists = async (tableName: "users" | "appointments" | "results" | "insurance_info"): Promise<boolean> => {
  try {
    // Use a type-safe approach to check if a table exists
    const { count, error } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true })
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') { // Table doesn't exist error code
        return false;
      }
      console.error(`Error checking if table ${tableName} exists:`, error);
      return false;
    }
    
    // If we got here, the table exists
    return true;
  } catch (error) {
    console.error(`Error in checkTableExists for ${tableName}:`, error);
    return false;
  }
};

/**
 * SQL statements needed to create tables in Supabase
 * These would typically be run through the Supabase dashboard or migrations
 */
export const getCreateTableStatements = () => {
  return {
    users: `
      CREATE TABLE IF NOT EXISTS "users" (
        id UUID PRIMARY KEY REFERENCES auth.users,
        email TEXT UNIQUE NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        phone_number TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `,
    appointments: `
      CREATE TABLE IF NOT EXISTS "appointments" (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES auth.users NOT NULL,
        type TEXT NOT NULL,
        date DATE NOT NULL,
        time TEXT NOT NULL,
        doctor TEXT NOT NULL,
        location TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        notes TEXT,
        confirmation_number TEXT
      );
    `,
    results: `
      CREATE TABLE IF NOT EXISTS "results" (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES auth.users NOT NULL,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        date DATE NOT NULL,
        status TEXT NOT NULL,
        doctor TEXT NOT NULL,
        department TEXT,
        findings TEXT,
        recommendation TEXT,
        details JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  };
};

/**
 * Instructions for setting up required tables in Supabase
 */
export const getSetupInstructions = (): string => {
  return `
To set up the required tables in Supabase:

1. Go to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Go to the SQL Editor
4. Create and run the following SQL queries:

${getCreateTableStatements().users}

${getCreateTableStatements().appointments}

${getCreateTableStatements().results}

5. After creating the tables, set up Row Level Security (RLS) policies to secure your data
  `;
};

