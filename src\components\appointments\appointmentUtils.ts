import {
  Ultrasound,
  XRay,
  MRI,
  BloodTestIcon,
  UrineTestIcon,
  PathologyIcon,
} from "@/components/CustomIcons";
import { AppointmentType } from "./appointmentData";
import { Appointment } from "./AppointmentsList";
import { ReactNode, ReactElement } from "react";

export const getAppointmentTypeIcon = (type: string): ReactElement => {
  const typeMap: Record<string, ReactElement> = {
    "Ultrasound": Ultrasound({ size: 18 }),
    "X-Ray": XRay({ size: 18 }),
    "MRI": MRI({ size: 18 }),
    "Comprehensive Metabolic Panel": BloodTestIcon({ size: 18 }),
    "Lipid Profile": BloodTestIcon({ size: 18 }),
    "Complete Blood Count (CBC)": BloodTestIcon({ size: 18 }),
    "Glucose Testing": BloodTestIcon({ size: 18 }),
    "Thyroid Function Panel": BloodTestIcon({ size: 18 }),
    "Comprehensive Blood Chemistry": BloodTestIcon({ size: 18 }),
    "Iron Studies": BloodTestIcon({ size: 18 }),
    "Vitamin D Test": BloodTestIcon({ size: 18 }),
    "Urinalysis": UrineTestIcon({ size: 18 }),
    "24-Hour Urine Collection": UrineTestIcon({ size: 18 }),
    "Drug Screening": UrineTestIcon({ size: 18 }),
    "Vitamin Panel": PathologyIcon({ size: 18 }),
    "Hormone Panel": PathologyIcon({ size: 18 }),
  };

  return typeMap[type] || PathologyIcon({ size: 18 });
};

export const getAppointmentTypeById = (id: string): AppointmentType | undefined => {
  const appointmentTypes = [
    { id: "ultrasound", name: "Ultrasound", icon: Ultrasound({ size: 18 }), category: "Imaging" },
    { id: "xray", name: "X-Ray", icon: XRay({ size: 18 }), category: "Imaging" },
    { id: "mri", name: "MRI", icon: MRI({ size: 18 }), category: "Imaging" },
    // ... other types could be imported from appointmentData.ts instead of duplicating
  ];
  
  return appointmentTypes.find(type => type.id === id);
};
