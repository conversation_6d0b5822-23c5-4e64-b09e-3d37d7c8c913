import React from "react";

export const Ultrasound = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M2 12h2" />
    <path d="M6 12h2" />
    <path d="M10 12h2" />
    <path d="M18 9l-4 6" />
    <path d="M21 9a3 3 0 0 0-3-3h-1a3 3 0 0 0-2.83 2" />
    <path d="M18 21c3.5-1 5.5-4 5.5-9.5a9.5 9.5 0 0 0-11-9.47 9.5 9.5 0 0 0-11 9.47 9.5 9.5 0 0 0 5.5 9.5" />
  </svg>
);

export const XRay = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M12 2a4 4 0 0 1 4 4" />
    <path d="M8 2a4 4 0 0 0-4 4" />
    <rect width="16" height="16" x="4" y="6" rx="2" />
    <path d="M12 10v4" />
    <path d="M8 10v2" />
    <path d="M16 10v2" />
    <path d="M8 16h8" />
  </svg>
);

export const ECG = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M22 12h-4l-3 11L9 3l-3 9H2" />
  </svg>
);

export const MRI = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M14 3C14 1.8954 13.1046 1 12 1C10.8954 1 10 1.8954 10 3L10 21C10 22.1046 10.8954 23 12 23C13.1046 23 14 22.1046 14 21L14 3Z" />
    <path d="M7 8C7 6.89543 6.10457 6 5 6C3.89543 6 3 6.89543 3 8L3 16C3 17.1046 3.89543 18 5 18C6.10457 18 7 17.1046 7 16L7 8Z" />
    <path d="M21 8C21 6.89543 20.1046 6 19 6C17.8954 6 17 6.89543 17 8L17 16C17 17.1046 17.8954 18 19 18C20.1046 18 21 17.1046 21 16L21 8Z" />
  </svg>
);

export const BloodTestIcon = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M12 2v5.5m0 0a2.5 2.5 0 0 1 2.5 2.5c0 .94-.49 1.41-1.17 2.09-.67.67-1.33 1.34-1.33 2.28a2.5 2.5 0 0 0 5 0" />
    <path d="M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z" />
  </svg>
);

export const UrineTestIcon = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M8 2h8" />
    <path d="M12 14v7" />
    <path d="M12 14a4 4 0 0 0 4-4V6H8v4a4 4 0 0 0 4 4Z" />
  </svg>
);

export const PathologyIcon = ({ size = 24, ...props }: { size?: number; [key: string]: any }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M20.25 6v5.27a2 2 0 0 1-1.45 1.92l-2.4.74a2 2 0 0 0-1.45 1.92V16" />
    <path d="M11.25 8v5.27a2 2 0 0 1-1.45 1.92l-2.4.74a2 2 0 0 0-1.45 1.92" />
    <path d="M15 6v9.5a2 2 0 0 1-1.45 1.92l-2.4.74a2 2 0 0 0-1.45 1.92V22" />
    <path d="M6 22v-4" />
    <path d="M3 2l3 3.5L3 9" />
    <path d="M21 9l-3-3.5L21 2" />
  </svg>
);
