
/**
 * Security middleware to protect against common web vulnerabilities
 * For local network hosting protection
 */

// Security headers configuration
export const securityHeaders = {
  // Prevents clickjacking attacks
  'X-Frame-Options': 'DENY',
  // Helps prevent XSS attacks
  'X-XSS-Protection': '1; mode=block',
  // Prevents MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  // Controls which features/APIs the site can use
  'Feature-Policy': "camera 'none'; microphone 'none'; geolocation 'none'",
  // Controls which resources can be loaded
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'",
  // Strict transport security (if you eventually use HTTPS)
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
};

// Apply security headers to Express or similar server
export const applySecurityHeaders = (req: any, res: any, next: any) => {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });
  next();
};
