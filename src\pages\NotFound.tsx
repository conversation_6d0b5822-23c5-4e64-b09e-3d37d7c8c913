
import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <>
      <Navbar />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-health-50/50 to-white pt-20 pb-16">
        <div className="container mx-auto px-4 py-8 text-center">
          <div className="relative mb-8 inline-block">
            <div className="absolute inset-0 bg-health-200 rounded-full transform rotate-12 opacity-30"></div>
            <h1 className="relative text-8xl font-bold text-health-600 z-10">404</h1>
          </div>
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Page Not Found</h2>
          <p className="text-xl text-slate-600 mb-8 max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              asChild
              className="bg-health-600 hover:bg-health-700 text-white neo-button"
            >
              <Link to="/">Return to Home</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-health-200 text-health-700"
            >
              <Link to="/contact">Contact Support</Link>
            </Button>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default NotFound;
